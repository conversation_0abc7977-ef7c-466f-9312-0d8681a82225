import React from "react";

const CancellationPolicy = () => {
  return (
    <div className="p-10 space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">
        Cancellation Policy
      </h1>

      <p className="text-lg font-medium text-white">
        At GamyDay, we aim to ensure a smooth and transparent tournament experience. Please read the following cancellation terms carefully:
      </p>

      <div className="mt-8 space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">1. No Participant Cancellations</h2>
          <p className="text-lg font-medium text-white">
            Once a participant has joined a tournament, <em>cancellations are strictly not permitted</em> under any circumstances.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">2. Host-Initiated Cancellations</h2>
          <p className="text-lg font-medium text-white">
            Only the <em>tournament host</em> holds the authority to cancel a tournament at their sole discretion.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">3. Liability Disclaimer</h2>
          <p className="text-lg font-medium text-white">
            GamyDay is <em>not liable</em> for any loss, inconvenience, or damages resulting from:
          </p>
          <ul className="list-disc pl-8 mt-2">
            <li className="text-lg font-medium text-white">Cancellations initiated by the tournament host</li>
            <li className="text-lg font-medium text-white">Non-participation by the player after joining</li>
          </ul>
        </div>

        <div className="bg-gray-800 p-4 rounded-lg border-l-4 border-yellow-500 mt-6">
          <p className="text-lg font-medium text-white">
            <strong>Note:</strong> We strongly recommend all participants to <em>review tournament details carefully</em> before joining to avoid any issues.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CancellationPolicy;
