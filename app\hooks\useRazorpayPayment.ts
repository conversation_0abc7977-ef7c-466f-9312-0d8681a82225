import { getR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/app/utils/razorpay";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { toast } from "react-toastify";
import { useState } from "react";

interface PaymentConfig {
  name: string;
  email: string;
  phone: string;
  accessToken: string;
}

interface CreateOrderParams {
  tournament_id: string;
  slot_id: string;
}

interface CreateBookingParams {
  tournament_id: string;
  slot_id: string;
  amount: number;
}

export const useRazorpayPayment = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createOrder = async (params: CreateOrderParams, accessToken: string) => {
    try {
      setIsLoading(true);
      const response = await api.post(API_ENDPOINTS.CREATE_BOOKING_ORDER, params, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response?.data?.data;
    } catch (error: any) {
      setError(error?.response?.data?.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const createDirectBooking = async (params: CreateBookingParams, accessToken: string) => {
    try {
      setIsLoading(true);
      const response = await api.post(API_ENDPOINTS.BOOKINGS, params, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response?.data?.data;
    } catch (error: any) {
      setError(error?.response?.data?.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const initializePayment = async (
    orderData: any,
    paymentConfig: PaymentConfig,
    onSuccess: (bookingDetails: any) => void
  ) => {
    const razorpayKey = getRazorpayKey();
    const options = {
      key: razorpayKey,
      amount: orderData?.amount,
      currency: "INR",
      name: "Gamyday",
      description: "Tournament Registration fee",
      image: "http://example.com/your_logo",
      order_id: orderData?.id,
      handler: async function (paymentDetails: any) {
        setIsLoading(true);
        try {
          const dataToSend = {
            razorpay_payment_id: paymentDetails.razorpay_payment_id,
            razorpay_order_id: paymentDetails.razorpay_order_id,
            razorpay_signature: paymentDetails.razorpay_signature,
            tournament_id: orderData.tournament_id,
            slot_id: orderData.slot_id,
            amount: orderData?.amount / 100,
          };
          
          const res = await api.post(API_ENDPOINTS.BOOKINGS, dataToSend, {
            headers: {
              Authorization: `Bearer ${paymentConfig.accessToken}`,
            },
          });
          
          if (res.status === 200) {
            onSuccess(res?.data?.data);
            toast.success("Tournament booked successfully!");
          }
        } catch (error: any) {
          setError(error?.response?.data?.message);
        } finally {
          setIsLoading(false);
        }
      },
      prefill: {
        name: paymentConfig.name,
        email: paymentConfig.email,
        contact: paymentConfig.phone,
      },
      notes: {
        address: "Razorpay Corporate Office",
      },
      theme: {
        color: "#3399cc",
      },
    };

    const razor = new (window as any).Razorpay(options);
    razor.open();
  };

  return {
    createOrder,
    initializePayment,
    createDirectBooking,
    isLoading,
    error,
  };
}; 