import { Game, Lobby, GameBooking, GamesResponse } from '../types/CommonComponent.types';

// Mock data for demonstration while backend APIs are being implemented
export const mockGames: Game[] = [
  {
    game_id: 'game_001',
    name: 'Battle Royale Arena',
    description: 'Fast-paced battle royale with 100 players competing for victory',
    image: '/images/games/battle-royale.jpg',
    category: 'Battle Royale',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-06T00:00:00Z'
  },
  {
    game_id: 'game_002',
    name: 'Strategy Masters',
    description: 'Turn-based strategy game requiring tactical thinking',
    image: '/images/games/strategy.jpg',
    category: 'Strategy',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-06T00:00:00Z'
  },
  {
    game_id: 'game_003',
    name: 'Racing Championship',
    description: 'High-speed racing with customizable vehicles',
    image: '/images/games/racing.jpg',
    category: 'Racing',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-06T00:00:00Z'
  },
  {
    game_id: 'game_004',
    name: 'Puzzle Quest',
    description: 'Mind-bending puzzles with increasing difficulty',
    image: '/images/games/puzzle.jpg',
    category: 'Puzzle',
    is_active: false,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-06T00:00:00Z'
  }
];

export const mockLobbies: { [gameId: string]: Lobby[] } = {
  'game_001': [
    {
      lobby_id: 'lobby_001',
      game_id: 'game_001',
      name: 'Beginners Arena',
      description: 'Perfect for new players',
      max_players: 50,
      current_players: 23,
      entry_fee: 50,
      prize_pool: 2500,
      status: 'waiting',
      start_time: '2025-07-06T20:00:00Z',
      is_joinable: true,
      created_at: '2025-07-06T18:00:00Z'
    },
    {
      lobby_id: 'lobby_002',
      game_id: 'game_001',
      name: 'Pro Championship',
      description: 'For experienced players only',
      max_players: 100,
      current_players: 78,
      entry_fee: 200,
      prize_pool: 20000,
      status: 'waiting',
      start_time: '2025-07-06T21:00:00Z',
      is_joinable: true,
      created_at: '2025-07-06T18:30:00Z'
    }
  ],
  'game_002': [
    {
      lobby_id: 'lobby_003',
      game_id: 'game_002',
      name: 'Quick Match',
      description: '30-minute strategic battle',
      max_players: 8,
      current_players: 6,
      entry_fee: 100,
      prize_pool: 800,
      status: 'waiting',
      start_time: '2025-07-06T19:30:00Z',
      is_joinable: true,
      created_at: '2025-07-06T18:45:00Z'
    }
  ]
};

// Mock service with delay to simulate API calls
export class MockGamesService {
  private static delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static async getAllGames(): Promise<GamesResponse> {
    await this.delay(800); // Simulate network delay
    return {
      data: mockGames,
      count: mockGames.length
    };
  }

  static async getGameLobbies(gameId: string): Promise<Lobby[]> {
    await this.delay(600);
    return mockLobbies[gameId] || [];
  }

  static async createGameBooking(bookingData: any): Promise<GameBooking> {
    await this.delay(1000);
    const booking: GameBooking = {
      booking_id: `booking_${Date.now()}`,
      game_id: bookingData.game_id,
      lobby_id: bookingData.lobby_id,
      game_name: bookingData.game_name,
      user_id: 'user_123',
      status: 'confirmed',
      entry_fee: 100,
      booking_time: new Date().toISOString(),
      game_start_time: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
      return_url: bookingData.return_to
    };
    return booking;
  }
}
