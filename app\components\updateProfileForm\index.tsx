"use client";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { setUser } from "@/redux/slices/userSlice";
import { UpdateProfileFormProps } from "@/app/types/CommonComponent.types";
import { generateRandomPAN } from "@/app/utils/helper";
import {
  UpdateProfileFormData,
  UpdateProfileFormSchema,
} from "@/app/schema/updateProfileSchema";

const UpdateProfileForm: React.FC<UpdateProfileFormProps> = ({
  handleModal,
  user,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [apiError, setApiError] = useState<string | null>(null);
  const [noPanCard, setNoPanCard] = useState<boolean>(false);
  const [noInstagram, setNoInstagram] = useState<boolean>(false);
  const [noYouTube, setNoYouTube] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<UpdateProfileFormData>({
    resolver: zodResolver(UpdateProfileFormSchema),
  });

  useEffect(() => {
    setValue("name", user.name);
    setValue("upi_id", user.upi_id);
    setValue("pan_number", user.pan_number);
    setValue("instagram_link", user.instagram_link || "");
    setValue("youtube_link", user.youtube_link || "");
    
    // Set checkbox states based on existing values
    setNoPanCard(!user.pan_number || user.pan_number.trim() === "");
    setNoInstagram(!user.instagram_link || user.instagram_link.trim() === "");
    setNoYouTube(!user.youtube_link || user.youtube_link.trim() === "");
  }, [setValue, user]);

  const handleNoPanCardChange = (checked: boolean) => {
    setNoPanCard(checked);
    if (checked) {
      setValue("pan_number", generateRandomPAN());
    } else {
      setValue("pan_number", "");
    }
  };

  const handleNoInstagramChange = (checked: boolean) => {
    setNoInstagram(checked);
    if (checked) {
      setValue("instagram_link", "https://www.instagram.com/gamyday.ig/");
    }
  };

  const handleNoYouTubeChange = (checked: boolean) => {
    setNoYouTube(checked);
    if (checked) {
      setValue("youtube_link", "https://www.youtube.com/@gamyday");
    }
  };

  const onSubmit = async (data: UpdateProfileFormData) => {
    setApiError(null);
    try {
      const res = await api.patch(API_ENDPOINTS.GET_USER, data);
      if (res.status === 200) {
        dispatch(setUser(res?.data?.data));
        toast.success("Profile updated sucessfully!");
        handleModal(false);
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  return (
    <div className="w-[450px]">
      <h2 className="text-2xl font-semibold text-white mb-6 text-center">
        Update Your Profile Details
      </h2>

      {apiError && (
        <div className="mb-4 p-2 bg-red-500 text-white rounded">{apiError}</div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Name
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your name"
            {...register("name")}
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            UPI ID
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your UPI ID"
            {...register("upi_id")}
          />
          {errors.upi_id && (
            <p className="text-red-500 text-sm mt-1">{errors.upi_id.message}</p>
          )}
        </div>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            PAN Number
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your PAN Number"
            {...register("pan_number")}
            disabled={noPanCard}
          />
          {errors.pan_number && (
            <p className="text-red-500 text-sm mt-1">
              {errors.pan_number.message}
            </p>
          )}
          <div className="mt-2">
            <label className="flex items-center text-white text-sm">
              <input
                type="checkbox"
                checked={noPanCard}
                onChange={(e) => handleNoPanCardChange(e.target.checked)}
                className="mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              I don&apos;t have a PAN card (check this box to proceed)
            </label>
          </div>
        </div>

        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Instagram Link
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your Instagram profile link"
            {...register("instagram_link")}
            disabled={noInstagram}
            value={noInstagram ? "" : undefined}
          />
          {errors.instagram_link && (
            <p className="text-red-500 text-sm mt-1">
              {errors.instagram_link.message}
            </p>
          )}
          <div className="mt-2">
            <label className="flex items-center text-white text-sm">
              <input
                type="checkbox"
                checked={noInstagram}
                onChange={(e) => handleNoInstagramChange(e.target.checked)}
                className="mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              I don&apos;t have an Instagram account
            </label>
          </div>
        </div>

        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            YouTube Link
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your YouTube channel link"
            {...register("youtube_link")}
            disabled={noYouTube}
            value={noYouTube ? "" : undefined}
          />
          {errors.youtube_link && (
            <p className="text-red-500 text-sm mt-1">
              {errors.youtube_link.message}
            </p>
          )}
          <div className="mt-2">
            <label className="flex items-center text-white text-sm">
              <input
                type="checkbox"
                checked={noYouTube}
                onChange={(e) => handleNoYouTubeChange(e.target.checked)}
                className="mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              I don&apos;t have a YouTube channel
            </label>
          </div>
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {isSubmitting ? "Updating..." : "Update"}
          </button>
        </div>
      </form>
    </div>
  );
};
export default UpdateProfileForm;
