"use client";

import React, { useEffect, useState, useRef } from "react";
import { BellIcon } from "@heroicons/react/24/outline";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import ReactMarkdown from "react-markdown";

interface Announcement {
  text: string;
}

const AnnouncementBell = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [showAnnouncements, setShowAnnouncements] = useState(false);
  const [hasNewAnnouncements, setHasNewAnnouncements] = useState(true);
  const announcementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const response = await api.get(API_ENDPOINTS.GET_ANNOUNCEMENTS);
        if (response.status === 200) {
          setAnnouncements(response.data);
          // Check if there are any announcements to show
          setHasNewAnnouncements(response.data.length > 0);
        }
      } catch (error) {
        console.error("Error fetching announcements:", error);
      }
    };

    fetchAnnouncements();
  }, []);

  useEffect(() => {
    // Handle clicks outside the announcement popup to close it
    const handleClickOutside = (event: MouseEvent) => {
      if (
        announcementRef.current &&
        !announcementRef.current.contains(event.target as Node)
      ) {
        setShowAnnouncements(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  
  // Control body scroll when announcement modal is open
  useEffect(() => {
    if (showAnnouncements) {
      // Prevent scrolling on the main page when modal is open
      document.body.classList.add('overflow-hidden');
    } else {
      // Re-enable scrolling when modal is closed
      document.body.classList.remove('overflow-hidden');
    }
    
    // Cleanup function to ensure scrolling is re-enabled when component unmounts
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [showAnnouncements]);

  const toggleAnnouncements = () => {
    setShowAnnouncements(!showAnnouncements);
    if (hasNewAnnouncements) {
      setHasNewAnnouncements(false);
    }
  };
  
  // Prevent wheel events from propagating to the background
  const handleWheel = (e: React.WheelEvent) => {
    // Stop the event from propagating to parent elements
    e.stopPropagation();
  };

  // Add custom styles for markdown content
  const markdownStyles = `
    .markdown-content h1, .markdown-content h2, .markdown-content h3, 
    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
      font-weight: bold;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }
    .markdown-content h1 { font-size: 1.3rem; }
    .markdown-content h2 { font-size: 1.1rem; }
    .markdown-content h3 { font-size: 1rem; }
    .markdown-content p { margin-bottom: 0.75rem; font-size: 0.9rem; }
    .markdown-content ul, .markdown-content ol { padding-left: 1.5rem; margin-bottom: 0.75rem; font-size: 0.9rem; }
    .markdown-content li { margin-bottom: 0.25rem; }
    .markdown-content a { color: #c9ff88; text-decoration: underline; }
    .markdown-content blockquote { border-left: 3px solid #c9ff88; padding-left: 1rem; margin-left: 0; margin-right: 0; font-style: italic; font-size: 0.9rem; }
    .markdown-content code { background-color: rgba(255, 255, 255, 0.1); padding: 0.1rem 0.2rem; border-radius: 3px; font-family: monospace; font-size: 0.85rem; }
    .markdown-content pre { background-color: rgba(255, 255, 255, 0.1); padding: 0.75rem; border-radius: 5px; overflow-x: auto; margin-bottom: 0.75rem; font-size: 0.85rem; }
    .markdown-content img { max-width: 100%; height: auto; border-radius: 5px; margin: 0.75rem 0; }
    .markdown-content table { border-collapse: collapse; width: 100%; margin-bottom: 1rem; font-size: 0.9rem; }
    .markdown-content th, .markdown-content td { border: 1px solid rgba(255, 255, 255, 0.2); padding: 0.5rem; text-align: left; }
    .markdown-content th { background-color: rgba(255, 255, 255, 0.1); }
    .markdown-content tr:nth-child(even) { background-color: rgba(255, 255, 255, 0.05); }
  `;


  return (
    <div className="relative">
      <style jsx>{markdownStyles}</style>
      <button
        onClick={toggleAnnouncements}
        className="relative flex items-center justify-center"
        aria-label="View announcements"
      >
        <BellIcon
          className={`h-6 w-6 ${hasNewAnnouncements ? "text-[#c9ff88]" : "text-white"} hover:text-[#c9ff88] transition-colors duration-200 ${hasNewAnnouncements ? "animate-bounce" : ""}`}
        />
        {hasNewAnnouncements && (
          <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 animate-pulse"></span>
        )}
      </button>

      {showAnnouncements && (
        <>
          {/* Dark overlay behind the modal */}
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
          
          <div
             ref={announcementRef}
             className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] bg-[#141517] rounded-[30px] border border-[#707070] text-white shadow-lg z-50"
             onWheel={handleWheel}
           >
          <div className="flex p-6">
            <div className="w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-[#c9ff88] font-bold text-xl">Announcements</h3>
                <button 
                  onClick={() => setShowAnnouncements(false)}
                  className="text-white hover:text-[#c9ff88] transition-colors duration-200"
                >
                  ✕
                </button>
              </div>
              
              {/* <div className="bg-[#c9ff88] py-2 px-2.5 rounded-[10px] mb-4">
                <div className="space-y-1">
                  <p className="text-black text-base font-bold">Latest Updates</p>
                </div>
              </div> */}
              
              <div className="max-h-[400px] overflow-y-auto announcement-scrollbar">
                {announcements.length > 0 ? (
                  announcements.map((announcement, index) => (
                    <div
                      key={index}
                      className="p-4 mb-3 border border-gray-700 rounded-[10px] last:mb-0"
                    >
                      <div className="text-white text-sm markdown-content prose prose-invert prose-headings:text-[#c9ff88] prose-a:text-[#c9ff88] prose-blockquote:border-[#c9ff88] prose-pre:bg-gray-800 prose-code:bg-gray-800 max-w-none">
                        <ReactMarkdown
                          components={{
                            a: ({...props}) => <a {...props} target="_blank" rel="noopener noreferrer" />,
                            img: ({...props}) => <img {...props} className="rounded-md my-2" />
                          }}
                        >
                          {announcement.text}
                        </ReactMarkdown>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 border border-gray-700 rounded-[10px]">
                    <p className="text-gray-400 text-center">No announcements available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        </>
      )}
    </div>
  );
};

export default AnnouncementBell;