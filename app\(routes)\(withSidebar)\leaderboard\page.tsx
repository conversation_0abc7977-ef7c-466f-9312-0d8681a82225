"use client";
import { useEffect, useState } from "react";
import { LeaderboardRes, UserLeaderboardStatus } from "@/app/types/CommonComponent.types";
import Loader from "@/app/components/common/Loader";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tableHeadings = [
  { id: 1, label: "Rank" },
  { id: 2, label: "Name" },
  { id: 3, label: "Total Winnings" },
  { id: 4, label: "Instagram" },
  { id: 5, label: "YouTube" },
];

// Function to get rank badge based on leaderboard position
const getRankBadge = (rank: number): string => {
  if (rank >= 1 && rank <= 20) return "/ranks/diamond.png";
  if (rank >= 21 && rank <= 50) return "/ranks/platinum.png";
  if (rank >= 51 && rank <= 100) return "/ranks/gold.png";
  if (rank >= 101 && rank <= 500) return "/ranks/silver.png";
  if (rank >= 501 && rank <= 1000) return "/ranks/bronze.png";
  if (rank >= 1001 && rank <= 5000) return "/ranks/iron.png";
  if (rank >= 5001 && rank <= 10000) return "/ranks/wood.png";
  return "/ranks/unranked.png"; // 10001+
};

// Function to get rank name for alt text
const getRankName = (rank: number): string => {
  if (rank >= 1 && rank <= 20) return "Diamond";
  if (rank >= 21 && rank <= 50) return "Platinum";
  if (rank >= 51 && rank <= 100) return "Gold";
  if (rank >= 101 && rank <= 500) return "Silver";
  if (rank >= 501 && rank <= 1000) return "Bronze";
  if (rank >= 1001 && rank <= 5000) return "Iron";
  if (rank >= 5001 && rank <= 10000) return "Wood";
  return "Unranked"; // 10001+
};

// Rank reference data for the reference table
const rankTiers = [
  { name: "Diamond", range: "Ranks 1-20", image: "/ranks/diamond.png" },
  { name: "Platinum", range: "Ranks 21-50", image: "/ranks/platinum.png" },
  { name: "Gold", range: "Ranks 51-100", image: "/ranks/gold.png" },
  { name: "Silver", range: "Ranks 101-500", image: "/ranks/silver.png" },
  { name: "Bronze", range: "Ranks 501-1000", image: "/ranks/bronze.png" },
  { name: "Iron", range: "Ranks 1001-5000", image: "/ranks/iron.png" },
  { name: "Wood", range: "Ranks 5001-10000", image: "/ranks/wood.png" },
  { name: "Unranked", range: "Ranks 10001+", image: "/ranks/unranked.png" },
];

const LeaderboardPage = () => {
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardRes>(
    {} as LeaderboardRes
  );
  const [userLeaderboardStatus, setUserLeaderboardStatus] = useState<UserLeaderboardStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUserStatusLoading, setIsUserStatusLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const totalPages = Math.ceil(leaderboardData?.count / itemsPerPage);

  const fetchLeaderboard = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.LEADERBOARD(currentPage));
      if (response.status === 200) {
        setLeaderboardData(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };

  const fetchUserLeaderboardStatus = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.USER_LEADERBOARD_STATUS);
      if (response.status === 200) {
        setUserLeaderboardStatus(response?.data?.data);
      }
    } catch (error: any) {
      console.error("Error fetching user leaderboard status:", error);
    } finally {
      setIsUserStatusLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaderboard();
    fetchUserLeaderboardStatus();
  }, [currentPage]);

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <div className="p-8 w-full bg-[#141517] min-h-screen">
      <div className="pt-8 flex flex-row gap-8 justify-center px-4">
        {/* Main leaderboard section */}
        <div className="w-[1000px]">
          {isLoading && (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] p-8 flex justify-center items-center h-64">
              <Loader />
            </div>
          )}

          {!isLoading && leaderboardData?.results?.length === 0 && (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] p-8 text-center">
              <p className="text-white text-lg font-medium">
                No detail available
              </p>
            </div>
          )}

          {leaderboardData?.results?.length > 0 && (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] overflow-hidden">
              {/* Header */}
              <div className="bg-[#141517] border-b border-[#2a2c2e] p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Global</p>
                    <p className="font-bold text-white text-xl">Leaderboard</p>
                  </div>
                </div>
              </div>

              {/* Table Header */}
              <div className="bg-[#141517] border-b border-[#2a2c2e] p-4">
                <div className="grid grid-cols-5 gap-4">
                  {tableHeadings.map((heading, index) => (
                    <div key={heading.id} className={`${index === 0 ? "pl-4" : ""}`}>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">
                        {heading.label}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Leaderboard Entries */}
              <div className="divide-y divide-[#2a2c2e]">
                {leaderboardData?.results?.map((leader, index) => (
                  <div key={leader?.rank} className="bg-[#141517] p-4 hover:bg-[#1a1c1e] transition-colors">
                    <div className="grid grid-cols-5 gap-4 items-center">
                      {/* Rank */}
                      <div className="flex items-center space-x-3 pl-4">
                        <div className="w-2 h-8 bg-[#c9ff88] rounded-sm"></div>
                        <span className={`text-white font-bold ${
                          leader?.rank === 1
                            ? "text-2xl text-[#c9ff88]"
                            : leader?.rank === 2
                            ? "text-xl text-[#c9ff88]"
                            : leader?.rank === 3
                            ? "text-lg text-[#c9ff88]"
                            : "text-base"
                        }`}>
                          #{leader?.rank}
                        </span>
                      </div>
                      {/* Name */}
                      <div className="flex items-center space-x-3">
                        <div className="relative group">
                          <img
                            src={getRankBadge(leader?.rank)}
                            alt={`${getRankName(leader?.rank)} rank`}
                            className="w-9 h-9 object-contain cursor-help"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/ranks/unranked.png';
                            }}
                          />
                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            {getRankName(leader?.rank)}
                          </div>
                        </div>
                        <span className={`text-white font-semibold ${
                          leader?.rank === 1
                            ? "text-xl"
                            : leader?.rank === 2
                            ? "text-lg"
                            : leader?.rank === 3
                            ? "text-base"
                            : "text-sm"
                        }`}>
                          {leader?.name}
                        </span>
                      </div>

                      {/* Total Winnings */}
                      <div className="bg-[#1a1c1e] rounded-md p-3 border border-[#2a2c2e]">
                        <p className="text-white text-xs uppercase tracking-wider opacity-70 mb-1">Winnings</p>
                        <p className={`font-bold text-[#c9ff88] ${
                          leader?.rank === 1
                            ? "text-xl"
                            : leader?.rank === 2
                            ? "text-lg"
                            : leader?.rank === 3
                            ? "text-base"
                            : "text-sm"
                        }`}>
                          ₹{leader?.total_winnings.toLocaleString("en-IN")}
                        </p>
                      </div>

                      {/* Instagram */}
                      <div className="flex justify-center">
                        {leader?.instagram_link ? (
                          <a href={leader.instagram_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-md hover:from-purple-600 hover:to-pink-600 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                            Instagram
                          </a>
                        ) : (
                          <span className="text-gray-500 text-sm">-</span>
                        )}
                      </div>

                      {/* YouTube */}
                      <div className="flex justify-center">
                        {leader?.youtube_link ? (
                          <a href={leader.youtube_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                            </svg>
                            YouTube
                          </a>
                        ) : (
                          <span className="text-gray-500 text-sm">-</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* User's leaderboard status box */}
        <div className="w-80">
          {isUserStatusLoading ? (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] p-6 h-64 flex items-center justify-center">
              <Loader />
            </div>
          ) : userLeaderboardStatus ? (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] overflow-hidden">
              {/* Header */}
              <div className="bg-[#141517] border-b border-[#2a2c2e] p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                  <div>
                    <p className="text-white text-xs uppercase tracking-wider opacity-70">Your</p>
                    <p className="font-bold text-white text-xl">Ranking</p>
                  </div>
                </div>
              </div>

              <div className="p-4 space-y-3">
                {/* Rank */}
                <div className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">Rank</p>
                    </div>
                    <span className="text-white font-bold text-xl text-[#c9ff88]">
                      #{userLeaderboardStatus.rank}
                    </span>
                  </div>
                </div>

                {/* Name */}
                <div className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">Name</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="relative group">
                        <img
                          src={getRankBadge(userLeaderboardStatus.rank)}
                          alt={`${getRankName(userLeaderboardStatus.rank)} rank`}
                          className="w-8 h-8 object-contain cursor-help"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/ranks/unranked.png';
                          }}
                        />
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                          {getRankName(userLeaderboardStatus.rank)}
                        </div>
                      </div>
                      <span className="text-white font-semibold">{userLeaderboardStatus.name}</span>
                    </div>
                  </div>
                </div>

                {/* Winnings */}
                <div className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">Winnings</p>
                    </div>
                    <span className="text-white font-bold text-[#c9ff88]">
                      ₹{userLeaderboardStatus.total_winnings.toLocaleString("en-IN")}
                    </span>
                  </div>
                </div>
                {/* Instagram */}
                <div className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">Instagram</p>
                    </div>
                    <div>
                      {userLeaderboardStatus.instagram_link ? (
                        <a href={userLeaderboardStatus.instagram_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-md hover:from-purple-600 hover:to-pink-600 transition-all duration-200">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                          </svg>
                          Instagram
                        </a>
                      ) : (
                        <span className="text-gray-500 text-sm">-</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* YouTube */}
                <div className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                      <p className="text-white text-xs uppercase tracking-wider opacity-70">YouTube</p>
                    </div>
                    <div>
                      {userLeaderboardStatus.youtube_link ? (
                        <a href={userLeaderboardStatus.youtube_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-all duration-200">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                          </svg>
                          YouTube
                        </a>
                      ) : (
                        <span className="text-gray-500 text-sm">-</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] p-6 h-64 flex items-center justify-center">
              <p className="text-gray-400 text-center">Login to view your ranking</p>
            </div>
          )}

          {/* Rank Reference Table */}
          <div className="bg-[#1a1c1e] rounded-lg border border-[#2a2c2e] mt-4 overflow-hidden">
            {/* Header */}
            <div className="bg-[#141517] border-b border-[#2a2c2e] p-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
                <div>
                  <p className="text-white text-xs uppercase tracking-wider opacity-70">Rank</p>
                  <p className="font-bold text-white text-lg">Tiers</p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <div className="space-y-2">
                {rankTiers.map((tier) => (
                  <div key={tier.name} className="bg-[#141517] rounded-md p-3 border border-[#2a2c2e]">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        <div className="relative group">
                          <img
                            src={tier.image}
                            alt={`${tier.name} rank`}
                            className="w-8 h-8 object-contain cursor-help"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/ranks/unranked.png';
                            }}
                          />
                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            {tier.name}
                          </div>
                        </div>
                        <span className="text-white font-medium text-sm">{tier.name}</span>
                      </div>
                      <span className="text-white text-xs uppercase tracking-wider opacity-70">{tier.range}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {leaderboardData?.count > 0 && (
        <div className="mt-6 flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      )}
    </div>
  );
};

export default LeaderboardPage;
