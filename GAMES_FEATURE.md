# Games Feature Documentation

## Overview
This document describes the new Games feature added to the GameDay frontend application. The feature allows users to browse available games, join lobbies, create bookings, and track their game participation.

## New API Endpoints Added

### Games API Endpoints
All endpoints are protected and require user authentication:

1. **GET /api/games** - Get list of all games
2. **GET /api/games/<game_id>** - Get details of a specific game
3. **GET /api/games/<game_id>/lobbies** - Get all lobbies for a specific game
4. **GET /api/lobbies/<lobby_id>** - Get details of a specific lobby
5. **POST /api/games/bookings** - Create a new game booking
6. **GET /api/games/results/<result_id>** - Get game result details
7. **GET /api/games/bookings** - Get user's game bookings list

### API Configuration
- Base URL: Configured via `NEXT_PUBLIC_API_URL` environment variable
- Authentication: Bearer token from cookies (`access_token`)
- Auto token refresh: Implemented for expired tokens

## New Components

### 1. GamesSection (`/app/components/gamesSection/`)
Main container component that manages the navigation between games list and lobby selection.

**Features:**
- State management for current view (games list vs lobby selection)
- Handles game selection and navigation flow
- Integrates with booking success callbacks

### 2. GamesGrid (`/app/components/gamesGrid/`)
Displays a responsive grid of available games.

**Features:**
- Fetches and displays all available games
- Responsive grid layout (1-4 columns based on screen size)
- Game status indicators (active/inactive)
- Error handling with retry functionality
- Loading states

### 3. GameCard (`/app/components/gamesGrid/GameCard.tsx`)
Individual game card component with hover effects and game information.

**Features:**
- Game image with fallback to initial letter
- Status badges for active/inactive games
- Category tags
- Hover animations and effects
- "Play Now" action button

### 4. GameLobbies (`/app/components/gameLobbies/`)
Shows available lobbies for a selected game and handles lobby joining.

**Features:**
- Displays lobby information (players, entry fee, prize pool, etc.)
- Real-time lobby status (waiting, active, completed, cancelled)
- Join lobby functionality with booking creation
- Back navigation to games list
- Loading and error states

### 5. GameBookingsList (`/app/components/gameBookings/`)
Lists user's game bookings with detailed information.

**Features:**
- Paginated booking list
- Booking status tracking
- Game results display
- Prize information
- Responsive layout

## New Types and Interfaces

### Game Types
```typescript
interface Game {
  game_id: string;
  name: string;
  description?: string;
  image?: string;
  category?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Lobby {
  lobby_id: string;
  game_id: string;
  name: string;
  max_players: number;
  current_players: number;
  entry_fee: number;
  prize_pool: number;
  status: 'waiting' | 'active' | 'completed' | 'cancelled';
  start_time: string;
  is_joinable: boolean;
}

interface GameBooking {
  booking_id: string;
  game_id: string;
  lobby_id: string;
  game_name: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  entry_fee: number;
  booking_time: string;
  game_start_time: string;
  result?: GameResult;
}
```

## Services

### GamesService (`/app/services/gamesService.ts`)
Centralized service for all game-related API calls with proper error handling and TypeScript typing.

**Methods:**
- `getAllGames()` - Fetch all available games
- `getGameDetails(gameId)` - Get specific game details
- `getGameLobbies(gameId)` - Get lobbies for a game
- `getLobbyDetails(lobbyId)` - Get specific lobby details
- `createGameBooking(bookingData)` - Create new game booking
- `getGameResult(resultId)` - Get game result details
- `getGameBookings(page, status?)` - Get user's bookings with pagination

## Integration

### Main Page Integration
The Games section is integrated into the main page (`/app/page.tsx`) and appears above the "Daily Paid Games" section and below the GameDay header.

### Authentication
All game-related API calls use the existing authentication system:
- Access tokens stored in cookies
- Automatic token refresh on expiration
- Redirect to login page on authentication failure

### Error Handling
- Toast notifications for user feedback
- Retry mechanisms for failed requests
- Graceful fallbacks for missing data
- Loading states for better UX

## Styling
- Consistent with existing GameDay design system
- Dark theme with accent color `#c9ff88`
- Responsive design for mobile and desktop
- Hover effects and smooth transitions
- Gradient backgrounds and border effects

## Usage Flow

1. **Browse Games**: Users see a grid of available games on the main page
2. **Select Game**: Click on a game card to view available lobbies
3. **Join Lobby**: Select a lobby and create a booking
4. **Track Progress**: View booking status and results in the bookings list

## Environment Variables
Make sure these are configured in your `.env` file:
- `NEXT_PUBLIC_API_URL` - Backend API base URL
- `NEXT_PUBLIC_RAZORPAY_KEY` - For payment processing (if needed)

## Future Enhancements
- Real-time lobby updates via WebSocket
- Game filtering and search functionality
- Detailed game statistics and analytics
- Social features (friends, leaderboards)
- In-game chat integration
