import { z } from "zod";

const phoneRegex = new RegExp("^[6-9][0-9]{9}");
const PhoneSchema = z
  .string()
  .regex(phoneRegex, "Please enter a valid email or mobile number.")
  .length(10, "Please enter a valid email or mobile number.");

const EmailSchema = z
  .string()
  .email("Please enter a valid email or mobile number.");

export const LoginSchema = z.object({
  login_identifier: z.union([PhoneSchema, EmailSchema]).refine(() => true, {
    message: "Please enter a valid email or mobile number.",
  }),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

export type LoginFormData = z.infer<typeof LoginSchema>;
