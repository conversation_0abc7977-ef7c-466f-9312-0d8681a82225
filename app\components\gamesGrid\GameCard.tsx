"use client";
import React, { useState } from 'react';
import { Game } from '@/app/types/CommonComponent.types';
import Image from 'next/image';

interface GameCardProps {
  game: Game;
  onClick: () => void;
}

const GameCard: React.FC<GameCardProps> = ({ game, onClick }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div 
      className="bg-gradient-to-b from-[#1a2c38] to-[#121f28] border border-[#2a4451] rounded-lg overflow-hidden cursor-pointer transition-all duration-300 hover:border-[#c9ff88] hover:shadow-lg hover:shadow-[#c9ff88]/20 group"
      onClick={onClick}
    >
      {/* Game Image */}
      <div className="relative h-40 bg-gray-800 overflow-hidden">
        {game.image && !imageError ? (
          <Image
            src={game.image}
            alt={game.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-[#2a4451] to-[#1a2c38]">
            <div className="text-[#c9ff88] text-4xl font-bold">
              {game.name.charAt(0).toUpperCase()}
            </div>
          </div>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
            game.is_active 
              ? 'bg-green-500 text-white' 
              : 'bg-red-500 text-white'
          }`}>
            {game.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Game Info */}
      <div className="p-4">
        <h3 className="text-white font-bold text-lg mb-2 group-hover:text-[#c9ff88] transition-colors duration-300">
          {game.name}
        </h3>
        
        {game.description && (
          <p className="text-gray-400 text-sm mb-3 line-clamp-2">
            {game.description}
          </p>
        )}

        {game.category && (
          <div className="mb-3">
            <span className="inline-block bg-[#2a4451] text-[#c9ff88] text-xs px-2 py-1 rounded-full">
              {game.category}
            </span>
          </div>
        )}

        {/* Action Button */}
        <button className="w-full bg-[#c9ff88] text-black font-semibold py-2 px-4 rounded hover:bg-[#b8e877] transition-colors duration-300 group-hover:shadow-md">
          Play Now
        </button>
      </div>
    </div>
  );
};

export default GameCard;
