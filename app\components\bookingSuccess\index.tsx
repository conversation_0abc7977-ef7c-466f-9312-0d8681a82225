"use client";
import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  BookingSuccessProps,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import { formatDate } from "@/app/utils/helper";
import UpdateInGameNameForm from "../updateInGameNameForm";

const BookingSuccess: React.FC<BookingSuccessProps> = ({ data }) => {
  const [inGameNameRes, setInGameNameRes] =
    useState<UpdateInGameNameResponse | null>(null);

  const router = useRouter();
  const goToMyTournaments = () => {
    router.push(`/my-tournaments/${data?.booking_id}`);
  };

  const handleFormSuccess = (response: UpdateInGameNameResponse) => {
    setInGameNameRes(response);
  };

  return (
    <div className="flex flex-col gap-10 items-center">
      <div className="flex flex-col gap-[17px] items-center ">
        <div className="flex items-center gap-[18px]">
          <div>
            <Image
              src="/icons/success.svg"
              alt="Order success"
              height={55}
              width={55}
              className="h-7 w-7 md:h-[55px] md:w-[55px]"
            />
          </div>
          <div>
            <h2 className="text-2xl md:text-[42px] font-semibold text-white">
              Booking Successful
            </h2>
          </div>
        </div>
        <div>
          <p className="font-light text-xs md:text-sm  text-center text-white">
            Thank you, your payment has been successful and your tournament
            booking is now confirmed.
          </p>
        </div>
      </div>
      <div className="px-20 flex flex-col gap-[30px] w-[600px] items-cente ">
        <div className=" grid grid-cols-2 py-2 px-5 rounded">
          <div className="space-y-1">
            <p className="text-white">Tournament Name</p>
            <p className="font-bold text-white">{data?.tournament?.name}</p>
          </div>
          <div className="space-y-1">
            <p className="text-white text-base">Slot</p>
            <p className="font-bold text-white">
              <span className="font-semibold text-sm">
                <span>{data?.time_slot?.formatted_time}, </span>
                {formatDate(data?.tournament?.date)}
              </span>
            </p>
          </div>
        </div>
        {inGameNameRes && (
          <div className="flex gap-5 px-5 items-center ">
            <p className="text-white">Your In Game Name is : </p>
            <p className="font-bold text-white">
              {inGameNameRes?.in_game_name}
            </p>
          </div>
        )}
        {!inGameNameRes && (
          <UpdateInGameNameForm
            bookingId={data?.booking_id}
            onSuccess={handleFormSuccess}
            tournamentName={data?.tournament?.name}
          />
        )}
        <div className="flex justify-center">
          <button
            onClick={goToMyTournaments}
            disabled={!inGameNameRes}
            className="text-sm underline capitalize font-normal py-1 px-[30px] rounded border border-solid border-gray-200 disabled:text-black text-white disabled:cursor-not-allowed disabled:opacity-70 disabled:bg-gray-100 "
          >
            View Booking Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingSuccess;
