import { z } from "zod";

// Base schema with common validations
const baseAmountSchema = z
  .string()
  .refine((value) => value.trim() !== "", { message: "Amount is required" })
  .transform((value) => parseFloat(value))
  .refine((value) => value >= 10, { message: "Amount must be at least ₹10" });

// Schema for redeem wallet (requires amounts in tens)
export const RedeemWalletFormSchema = z.object({
  amount: baseAmountSchema.refine((value) => value % 10 === 0, { 
    message: "Amount must be in tens (10, 20, 30, etc.)" 
  }),
});

// Schema for UPI payments (any amount >= 10 is allowed)
export const UpiPaymentFormSchema = z.object({
  amount: baseAmountSchema,
});

// Default schema for backward compatibility
export const TopupWalletFormSchema = z.object({
  amount: baseAmountSchema.refine((value) => value % 10 === 0, { 
    message: "Amount must be in tens (10, 20, 30, etc.)" 
  }),
});

export type TopupWalletFormData = z.infer<typeof TopupWalletFormSchema>;
export type UpiPaymentFormData = z.infer<typeof UpiPaymentFormSchema>;
export type RedeemWalletFormData = z.infer<typeof RedeemWalletFormSchema>;
