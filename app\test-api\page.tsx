"use client";
import React, { useState } from "react";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { GamesService } from "@/app/services/gamesService";

const TestAPIPage = () => {
  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test: string, success: boolean, data: any, error?: any) => {
    setResults(prev => [...prev, {
      test,
      success,
      data: success ? data : null,
      error: success ? null : error?.message || error,
      timestamp: new Date().toISOString()
    }]);
  };

  const testEndpoints = async () => {
    setIsLoading(true);
    setResults([]);

    // Test 1: Get all games
    try {
      const games = await GamesService.getAllGames();
      addResult("GET /games", true, games);
    } catch (error) {
      addResult("GET /games", false, null, error);
    }

    // Test 2: Get game details for a specific game
    try {
      const gameDetails = await api.get(API_ENDPOINTS.GET_GAME_DETAILS("d11ao7kfntcgmbtc7g"));
      addResult("GET /games/{id}", true, gameDetails.data);
    } catch (error) {
      addResult("GET /games/{id}", false, null, error);
    }

    // Test 3: Get game lobbies
    try {
      const lobbies = await api.get(API_ENDPOINTS.GET_GAME_LOBBIES("d11ao7kfntcgmbtc7g"));
      addResult("GET /games/{id}/lobbies", true, lobbies.data);
    } catch (error) {
      addResult("GET /games/{id}/lobbies", false, null, error);
    }

    // Test 4: Test booking endpoints with dummy data
    const testBookingData = {
      game_id: "d11ao7kfntcgmbtc7g",
      lobby_id: "lobby-1",
      game_name: "Tic-Tac-Toe",
      return_to: window.location.origin + "/game-play"
    };

    try {
      const booking = await api.post(API_ENDPOINTS.CREATE_GAME_BOOKING, testBookingData);
      addResult("POST /games/bookings", true, booking.data);
    } catch (error) {
      addResult("POST /games/bookings", false, null, error);
    }

    // Test 5: Test user bookings endpoint
    const userBookingData = {
      tournament_id: "d11ao7kfntcgmbtc7g",
      time_slot_id: "lobby-1",
      amount: 0
    };

    try {
      const userBooking = await api.post(API_ENDPOINTS.BOOKINGS, userBookingData);
      addResult("POST /user/bookings", true, userBooking.data);
    } catch (error) {
      addResult("POST /user/bookings", false, null, error);
    }

    setIsLoading(false);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-white mb-6">API Endpoint Test</h1>
      
      <button 
        onClick={testEndpoints}
        disabled={isLoading}
        className="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? "Testing..." : "Test API Endpoints"}
      </button>

      {results.length > 0 && (
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-white mb-4">Test Results</h2>
          <div className="space-y-4">
            {results.map((result, index) => (
              <div 
                key={index}
                className={`p-4 rounded-md border ${
                  result.success 
                    ? 'bg-green-900/20 border-green-500' 
                    : 'bg-red-900/20 border-red-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <h3 className={`font-semibold ${
                    result.success ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {result.test} - {result.success ? 'SUCCESS' : 'FAILED'}
                  </h3>
                  <span className="text-gray-400 text-sm">{result.timestamp}</span>
                </div>
                
                {result.success && result.data && (
                  <div className="mt-2">
                    <h4 className="text-gray-300 font-medium">Response:</h4>
                    <pre className="bg-gray-800 p-2 rounded text-sm text-gray-300 overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
                
                {!result.success && result.error && (
                  <div className="mt-2">
                    <h4 className="text-red-300 font-medium">Error:</h4>
                    <pre className="bg-gray-800 p-2 rounded text-sm text-red-300">
                      {typeof result.error === 'string' ? result.error : JSON.stringify(result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestAPIPage;
