import { z } from "zod";

const panRegex = new RegExp("^[A-Z]{5}[0-9]{4}[A-Z]{1}$");
const instagramRegex = /^(https?:\/\/)?(www\.)?instagram\.com\/.*/i;
const youtubeRegex = /^(https?:\/\/)?(www\.)?youtube\.com\/.*/i;

export const UpdateProfileFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  upi_id: z.string().min(1, "UPI ID is required"),
  pan_number: z
    .string()
    .min(1, "PAN Number is required")
    .regex(panRegex, "Invalid PAN Number"),
  instagram_link: z
    .string()
    .optional()
    .refine(
      (val) => !val || instagramRegex.test(val),
      "Instagram link must contain www.instagram.com"
    ),
  youtube_link: z
    .string()
    .optional()
    .refine(
      (val) => !val || youtubeRegex.test(val),
      "YouTube link must contain www.youtube.com"
    ),
});

export type UpdateProfileFormData = z.infer<typeof UpdateProfileFormSchema>;
