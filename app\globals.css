@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  /* Neon scrollbar for announcement box */
  .announcement-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .announcement-scrollbar::-webkit-scrollbar-track {
    background: rgba(20, 21, 23, 0.5);
    border-radius: 10px;
  }
  
  .announcement-scrollbar::-webkit-scrollbar-thumb {
    background: #c9ff88;
    border-radius: 10px;
    box-shadow: 0 0 10px #c9ff88, 0 0 20px #c9ff88;
  }
  
  .announcement-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #d8ffaa;
    box-shadow: 0 0 15px #c9ff88, 0 0 25px #c9ff88;
  }
  
  /* Firefox scrollbar */
  .announcement-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #c9ff88 rgba(20, 21, 23, 0.5);
  }
}

/* Custom dot styles */
.slick-dots li button:before {
  color: white !important;
  font-size: 12px;
}

.slick-dots li.slick-active button:before {
  color: white !important;
}


@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.blinking-dot {
  width: 1rem; /* Adjust size as needed */
  height: 1rem; /* Adjust size as needed */
  background-color: #4caf50; /* Green color */
  border-radius: 50%;
  animation: blink 1s infinite; /* Adjust duration as needed */
}