import React from "react";

const JoiningPolicy = () => {
  return (
    <div className="p-10 space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Joining Policy</h1>

      <p className="text-lg font-medium text-white">
        At <em>GamyDay</em>, it is the sole responsibility of the player to correctly join any tournament or game they choose to participate in. Please read the following terms carefully before entering any match or competition.
      </p>

      <div className="space-y-6">
        <div>
          <h3 className="text-white font-semibold mb-2">
            1. Player Responsibility
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>
              All users are required to join games and tournaments strictly as per the instructions provided by GamyDay, including but not limited to codes, room IDs, passwords, and time schedules.
            </li>
            <li>
              It is the player&apos;s duty to follow joining procedures correctly and on time.
            </li>
          </ul>
        </div>

        <div>
          <h3 className="text-white font-semibold mb-2">
            2. Instructions Provided
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>
              GamyDay will clearly communicate all necessary joining information (such as codes, passwords, links, and steps) through the platform or official communication channels.
            </li>
            <li>
              Players must ensure they read and follow the instructions properly to enter the match or tournament.
            </li>
          </ul>
        </div>

        <div>
          <h3 className="text-white font-semibold mb-2">
            3. No Platform Liability for Joining Errors
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>
              GamyDay is <em>not liable</em> if a player fails to join a tournament or game due to:
              <ul className="list-disc pl-6 space-y-2 text-white mt-2">
                <li>Not understanding the instructions,</li>
                <li>Personal mistakes or delays,</li>
                <li>Misuse of the joining code,</li>
                <li>Lack of attention, or</li>
                <li>Technical issues on the player&apos;s side.</li>
              </ul>
            </li>
          </ul>
        </div>

        <div>
          <h3 className="text-white font-semibold mb-2">
            4. No Refunds or Compensation
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>
              No refunds, replacements, or adjustments will be provided for failure to join a game or tournament due to player error.
            </li>
            <li>
              Entry fees or tickets will not be transferred or reimbursed under such conditions.
            </li>
          </ul>
        </div>

        <div>
          <h3 className="text-white font-semibold mb-2">
            5. Acknowledgment
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>
              By participating in any paid or free contest, the user acknowledges that they are capable of following the joining process as instructed.
            </li>
            <li>
              If a user is unsure how to join, it is their responsibility to seek help <em>before</em> the game starts. Failure to do so will not be the liability of GamyDay.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default JoiningPolicy;