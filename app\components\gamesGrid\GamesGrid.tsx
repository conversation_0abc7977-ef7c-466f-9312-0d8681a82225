"use client";
import React, { useState, useEffect } from 'react';
import { Game } from '@/app/types/CommonComponent.types';
import { GamesService } from '@/app/services/gamesService';
import { MockGamesService } from '@/app/services/mockGamesService';
import Loader from '../common/Loader';
import { toast } from 'react-toastify';
import Image from 'next/image';

interface GamesGridProps {
  onGameSelect?: (game: Game) => void;
}

// Inline GameCard component
const GameCardComponent: React.FC<{ game: Game; onClick: () => void }> = ({ game, onClick }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div 
      className="bg-gradient-to-b from-[#1a2c38] to-[#121f28] border border-[#2a4451] rounded-lg overflow-hidden cursor-pointer transition-all duration-300 hover:border-[#c9ff88] hover:shadow-lg hover:shadow-[#c9ff88]/20 group"
      onClick={onClick}
    >
      {/* Game Image */}
      <div className="relative h-40 bg-gray-800 overflow-hidden">
        {game.image && !imageError ? (
          <Image
            src={game.image}
            alt={game.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-[#2a4451] to-[#1a2c38]">
            <div className="text-[#c9ff88] text-4xl font-bold">
              {game.name.charAt(0).toUpperCase()}
            </div>
          </div>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
            game.is_active 
              ? 'bg-green-500 text-white' 
              : 'bg-red-500 text-white'
          }`}>
            {game.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Game Info */}
      <div className="p-4">
        <h3 className="text-white font-bold text-lg mb-2 group-hover:text-[#c9ff88] transition-colors duration-300">
          {game.name}
        </h3>
        
        {game.description && (
          <p className="text-gray-400 text-sm mb-3 line-clamp-2">
            {game.description}
          </p>
        )}

        {game.category && (
          <div className="mb-3">
            <span className="inline-block bg-[#2a4451] text-[#c9ff88] text-xs px-2 py-1 rounded-full">
              {game.category}
            </span>
          </div>
        )}

        {/* Action Button */}
        <button className="w-full bg-[#c9ff88] text-black font-semibold py-2 px-4 rounded hover:bg-[#b8e877] transition-colors duration-300 group-hover:shadow-md">
          Play Now
        </button>
      </div>
    </div>
  );
};

const GamesGrid: React.FC<GamesGridProps> = ({ onGameSelect }) => {
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchGames();
  }, []);

  const fetchGames = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      try {
        // Try real API first
        const response = await GamesService.getAllGames();
        setGames(response.data);
      } catch (error: any) {
        // If real API fails, use mock data for demonstration
        console.log('Real API not available, using mock data:', error);
        const mockResponse = await MockGamesService.getAllGames();
        setGames(mockResponse.data);
      }
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch games';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGameClick = (game: Game) => {
    if (onGameSelect) {
      onGameSelect(game);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader />
      </div>
    );
  }

  if (error) {
    if (error === 'coming-soon') {
      return (
        <div className="bg-gradient-to-b from-[#1a2c38] to-[#121f28] border border-[#2a4451] rounded-lg p-8 text-center">
          <div className="space-y-4">
            <div className="text-6xl">🎮</div>
            <h2 className="text-2xl font-bold text-white">Games Coming Soon!</h2>
            <p className="text-gray-400 max-w-md mx-auto">
              We&apos;re working hard to bring you exciting games. The games section will be available once the backend APIs are implemented.
            </p>
            <div className="flex justify-center space-x-4 text-sm text-gray-500">
              <span>• Competitive Gaming</span>
              <span>• Real-time Lobbies</span>
              <span>• Prize Pools</span>
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="text-red-500 text-center p-4">
        <p>Error: {error}</p>
        <button 
          onClick={fetchGames}
          className="mt-2 px-4 py-2 bg-[#c9ff88] text-black rounded hover:bg-[#b8e877] transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (games.length === 0) {
    return (
      <div className="text-gray-400 text-center p-8">
        <p>No games available at the moment.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Available Games</h2>
        <div className="flex items-center space-x-3">
          <span className="text-gray-400 text-sm">{games.length} games available</span>
          <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">
            Demo Mode
          </span>
        </div>
      </div>
      
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4">
        <p className="text-blue-400 text-sm">
          🎮 <strong>Demo Version:</strong> This is a preview of the Games feature. Real games and lobbies will be available when backend APIs are implemented.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {games.map((game) => (
          <GameCardComponent
            key={game.game_id}
            game={game}
            onClick={() => handleGameClick(game)}
          />
        ))}
      </div>
    </div>
  );
};

export default GamesGrid;
