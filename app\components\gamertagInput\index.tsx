"use client";
import { useState } from "react";

interface GamertagInputProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (gamertag: string) => void;
  gameName: string;
  lobbyName: string;
  entryFee: number;
  isLoading?: boolean;
}

const GamertagInput: React.FC<GamertagInputProps> = ({
  isOpen,
  onClose,
  onSubmit,
  gameName,
  lobbyName,
  entryFee,
  isLoading = false
}) => {
  const [gamertag, setGamertag] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!gamertag.trim()) {
      setError("Please enter your gamertag");
      return;
    }
    
    if (gamertag.trim().length < 3) {
      setError("Gamertag must be at least 3 characters");
      return;
    }
    
    if (gamertag.trim().length > 20) {
      setError("Gamertag must be less than 20 characters");
      return;
    }
    
    setError("");
    onSubmit(gamertag.trim());
  };

  const handleClose = () => {
    setGamertag("");
    setError("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#141517] rounded-lg border border-[#707070] p-6 w-full max-w-md">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Join Game</h2>
          <div className="text-gray-400 text-sm space-y-1">
            <p><span className="font-medium">Game:</span> {gameName}</p>
            <p><span className="font-medium">Lobby:</span> {lobbyName}</p>
            <p><span className="font-medium">Entry Fee:</span> {entryFee === 0 ? "FREE" : `₹${entryFee}`}</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="gamertag" className="block text-white font-medium mb-2">
              What should we call you in-game?
            </label>
            <input
              type="text"
              id="gamertag"
              value={gamertag}
              onChange={(e) => {
                setGamertag(e.target.value);
                setError("");
              }}
              placeholder="Enter your gamertag"
              className="w-full px-4 py-3 bg-[#1a1a1c] border border-[#2a2c2e] rounded-md text-white placeholder-gray-500 focus:outline-none focus:border-[#c9ff88] focus:ring-1 focus:ring-[#c9ff88]"
              disabled={isLoading}
              autoFocus
              maxLength={20}
            />
            {error && (
              <p className="text-red-400 text-sm mt-1">{error}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              This name will be visible to other players in the game
            </p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-gray-600 text-white rounded-md font-medium hover:bg-gray-500 disabled:opacity-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !gamertag.trim()}
              className="flex-1 px-4 py-3 bg-[#c9ff88] text-[#070b28] rounded-md font-medium hover:bg-opacity-90 disabled:opacity-50 transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-[#070b28]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Joining...
                </>
              ) : (
                entryFee === 0 ? "Join Free Game" : `Pay ₹${entryFee} & Join`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GamertagInput;
