"use client";
import { useEffect, useState } from "react";
import {
    MyBookingInfo,
    MybookingsRes,
} from "@/app/types/CommonComponent.types";
import Loader from "@/app/components/common/Loader";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { formatDate } from "@/app/utils/helper";
import { useRouter } from "next/navigation";
import StartGameModal from "@/app/components/startGameModal";
import { Modal } from "@/app/components/modal";
import withAuth from "@/app/utils/withAuth";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tournamentTableHeadings = [

    { id: 1, label: "Booking ID" },
    { id: 2, label: "Booked on" },
    { id: 3, label: "Tournament" },
    { id: 4, label: "Slot" },
    { id: 5, label: "In Game Name" },
    { id: 6, label: "Link/Status" },
    { id: 7, label: "Result" },
    { id: 8, label: "Amount" },
    { id: 9, label: "Status" },
];

const gameTableHeadings = [
    { id: 1, label: "Booking ID" },
    { id: 2, label: "Booked on" },
    { id: 3, label: "Game" },
    { id: 4, label: "Result" },
    { id: 5, label: "Amount" },
    { id: 6, label: "Status" },
];

interface GameBooking {
    count: number;
    next: string | null;
    previous: string | null;
    results: GameResult[];
}

interface GameResult {
    booking_id: string;
    created_at: string;
    game_name: string;
    result: string | null;
    amount: string;
}

const MyTournamentsPage = () => {
    const [myBookings, setMyBookings] = useState<MybookingsRes>(
        {} as MybookingsRes
    );
    const [myGameBookings, setMyGameBookings] = useState<GameBooking>({
        count: 0,
        next: null,
        previous: null,
        results: [],
    });
    const [isLoading, setIsLoading] = useState(true);
    const [showGameDetailsModal, setShowGameDetailsModal] = useState(false);
    const [startGame, setStartGame] = useState<MyBookingInfo>();
    const [tournamentDetails, setTournamentDetails] = useState<any>(null);
    const [error, setError] = useState<any>(null);
    const [selectedStatus, setSelectedStatus] = useState<string | undefined>("booked");
    const [activeTab, setActiveTab] = useState<"tournaments" | "games">("tournaments");

    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const router = useRouter();

    const fetchMyTournaments = async () => {
        try {
            setIsLoading(true);
            const response = await api.get(
                API_ENDPOINTS.GET_BOOKINGS(currentPage, selectedStatus)
            );
            if (response.status === 200) {
                setMyBookings(response?.data);
            }
        } catch (error: any) {
            setError(error);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchMyGameBookings = async () => {
        try {
            setIsLoading(true);
            const params: { page: number; status?: string } = { page: currentPage };
            if (selectedStatus) {
                if (selectedStatus === "booked") {
                    params.status = "pending";
                } else {
                    params.status = selectedStatus;
                }
            }
            const response = await api.get(API_ENDPOINTS.GET_GAME_BOOKINGS(currentPage, params.status), {
                params,
            });
            if (response.status === 200) {
                setMyGameBookings(
                    response.data || { count: 0, next: null, previous: null, results: [] }
                );
            }
        } catch (error: any) {
            setError(error);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (activeTab === "tournaments") {
            fetchMyTournaments();
        } else {
            fetchMyGameBookings();
        }
    }, [currentPage, selectedStatus, activeTab]);

    const onRowClick = (bookingId: string) => {
        router.push(`/my-tournaments/${bookingId}`);
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "booked":
            case "pending":
                return "text-orange-500";
            case "cancelled":
                return "text-red-500";
            case "completed":
                return "text-green-500";
            case "in_progress":
                return "text-blue-500";
            default:
                return "text-gray-400";
        }
    };

    const getGameResultAndStatus = (result: string | null) => {
        if (result === "1") return { resultText: "Won", status: "completed" };
        if (result === null) return { resultText: "Lost", status: "completed" };
        if (result === "UNKNOWN")
            return { resultText: "Refunded", status: "cancelled" };
        return { resultText: "Pending", status: "pending" };
    };

    const totalPages =
        activeTab === "tournaments"
            ? Math.ceil(myBookings?.count / itemsPerPage)
            : Math.ceil(myGameBookings.count / itemsPerPage);

    if (error?.response?.status === 500) {
        return <ServerError />;
    }

    return (
        <>
            <div className="p-8 w-full">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <h1 className="text-3xl font-bold text-white">
                        MY TOURNAMENTS & GAMES
                    </h1>
                </div>

                {/* Tab Navigation */}
                <div className="flex gap-4 mb-6 mt-6">
                    <button
                        onClick={() => {
                            setActiveTab("tournaments");
                            setCurrentPage(1);
                        }}
                        className={`px-6 py-3 rounded-lg font-semibold transition-all ${activeTab === "tournaments"
                            ? "bg-[#c9ff88] text-[#070b28]"
                            : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                            }`}

                    >
                        Tournaments
                    </button>
                    <button
                        onClick={() => {
                            setActiveTab("games");
                            setCurrentPage(1);
                        }}
                        className={`px-6 py-3 rounded-lg font-semibold transition-all ${activeTab === "games"
                            ? "bg-[#c9ff88] text-[#070b28]"
                            : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                            }`}
                    >
                        Live Games
                    </button>
                </div>

                {/* Status Filter */}
                {activeTab === "tournaments" && (
                    <div className="flex flex-wrap gap-2 mb-6">
                        <button
                            onClick={() => {
                                setSelectedStatus("booked");
                                setCurrentPage(1);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-bold ${selectedStatus === "booked"
                                ? "bg-orange-500 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                        >
                            {activeTab === "tournaments" ? "Booked" : "Pending"}
                        </button>

                        <button
                            onClick={() => {
                                setSelectedStatus("completed");
                                setCurrentPage(1);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-bold ${selectedStatus === "completed"
                                ? "bg-green-600 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                        >
                            Completed
                        </button>
                        <button
                            onClick={() => {
                                setSelectedStatus("cancelled");
                                setCurrentPage(1);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-bold ${selectedStatus === "cancelled"
                                ? "bg-red-500 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                        >
                            Cancelled
                        </button>
                        <button
                            onClick={() => {
                                setSelectedStatus(undefined);
                                setCurrentPage(1);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-bold ${!selectedStatus
                                ? "bg-red-600 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                        >
                            All
                        </button>
                    </div>
                )}

                <div className="pt-8">
                    {isLoading && (
                        <div className="h-[50vh] flex justify-center items-center w-full">
                            <Loader />
                        </div>
                    )}

                    {!isLoading &&
                        activeTab === "tournaments" &&
                        myBookings?.results?.length === 0 && (
                            <div className="bg-[#141517] rounded-[20px] border border-[#707070] text-white p-6 text-center">
                                <p className="text-xl">
                                    You haven&apos;t booked any tournaments yet.
                                </p>
                            </div>
                        )}

                    {!isLoading &&
                        activeTab === "games" &&
                        myGameBookings?.results?.length === 0 && (
                            <div className="bg-[#141517] rounded-[20px] border border-[#707070] text-white p-6 text-center">
                                <p className="text-xl">You haven&apos;t booked any games yet.</p>
                            </div>
                        )}

                    {/* Tournament Table */}
                    {!isLoading &&
                        activeTab === "tournaments" &&
                        myBookings?.results?.length > 0 && (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-700">
                                    <thead>
                                        <tr>
                                            {tournamentTableHeadings.map((heading) => (
                                                <th
                                                    key={heading.id}
                                                    scope="col"
                                                    className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                                                >
                                                    {heading.label}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-800">
                                        {myBookings?.results?.map((booking) => (
                                            <tr
                                                key={booking.booking_id}
                                                className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                                                onClick={() => onRowClick(booking.booking_id)}
                                            >
                                                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-base font-medium">
                                                    {booking?.booking_id}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    {formatDate(booking?.created_at.split("T")[0])}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    {booking?.tournament_name}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    {booking?.time_slot},{" "}
                                                    {formatDate(booking?.tournament_date)}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    {booking?.in_game_name ?? "-"}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    <button
                                                        onClick={async (e) => {
                                                            e.stopPropagation();
                                                            setStartGame(booking);
                                                            try {
                                                                const response = await api.get(
                                                                    API_ENDPOINTS.GET_BOOKING_DETAILS(
                                                                        booking.booking_id
                                                                    )
                                                                );
                                                                if (response.status === 200) {
                                                                    setTournamentDetails(
                                                                        response.data.data.tournament
                                                                    );
                                                                }
                                                            } catch (error) {
                                                                console.error(
                                                                    "Error fetching booking details:",
                                                                    error
                                                                );
                                                            }
                                                            setShowGameDetailsModal(true);
                                                        }}
                                                        disabled={
                                                            booking.status?.toLowerCase() === "cancelled"
                                                        }
                                                        className={`justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm ${booking.status?.toLowerCase() === "cancelled"
                                                            ? "opacity-50 cursor-not-allowed"
                                                            : "hover:bg-red-500"
                                                            }`}
                                                    >
                                                        {booking.status?.toLowerCase() === "cancelled"
                                                            ? "Cancelled"
                                                            : "Start game"}
                                                    </button>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    {booking?.result ?? "-"}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    ₹
                                                    {Number(booking?.amount).toLocaleString("en-IN") ??
                                                        "-"}
                                                </td>
                                                <td
                                                    className={`whitespace-nowrap px-3 py-4 text-base font-semibold ${getStatusColor(
                                                        booking?.status
                                                    )}`}
                                                >
                                                    {booking?.status ?? "-"}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}

                    {/* Game Table */}
                    {!isLoading &&
                        activeTab === "games" &&
                        myGameBookings?.results?.length > 0 && (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-700">
                                    <thead>
                                        <tr>
                                            {gameTableHeadings.map((heading) => (
                                                <th
                                                    key={heading.id}
                                                    scope="col"
                                                    className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                                                >
                                                    {heading.label}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-800">
                                        {myGameBookings.results.map((booking) => {
                                            const { resultText, status } = getGameResultAndStatus(
                                                booking.result
                                            );
                                            return (
                                                <tr
                                                    key={booking.booking_id}
                                                    className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                                                >
                                                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-base font-medium">
                                                        {booking.booking_id}
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 text-base">
                                                        {formatDate(booking.created_at.split("T")[0])}
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 text-base">
                                                        {booking.game_name}
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 text-base">
                                                        {resultText}
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 text-base">
                                                        ₹{Number(booking.amount).toLocaleString("en-IN")}
                                                    </td>
                                                    <td
                                                        className={`whitespace-nowrap px-3 py-4 text-base font-semibold ${getStatusColor(
                                                            status
                                                        )}`}
                                                    >
                                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                        )}
                </div>

                {((activeTab === "tournaments" && myBookings?.count > 0) ||
                    (activeTab === "games" && myGameBookings.count > 0)) && (
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={(page) => setCurrentPage(page)}
                        />
                    )}
            </div>

            <Modal
                modalOpen={showGameDetailsModal}
                handleModalOpen={() => setShowGameDetailsModal(false)}
                showRefreshButton={true}
                onRefresh={fetchMyTournaments}
            >
                <StartGameModal
                    game_link={startGame?.game_link as string}
                    room_id={startGame?.room_id as string}
                    room_password={startGame?.room_password as string}
                    bookingId={startGame?.booking_id}
                    tournamentName={startGame?.tournament_name || ""}
                    inGameName={startGame?.in_game_name || ""}
                    showUpdateInGameNameForm={true}
                    onSuccess={() => fetchMyTournaments()}
                    createdBy={tournamentDetails?.created_by}
                />
            </Modal>
        </>
    );

};

export default withAuth(MyTournamentsPage);
