"use client";
import React, { useState } from "react";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import Image from "next/image";
import { PencilSquareIcon } from "@heroicons/react/24/outline";
import { Modal } from "@/app/components/modal";
import UpdateProfileForm from "@/app/components/updateProfileForm";
import withAuth from "@/app/utils/withAuth";

const ProfilePage = () => {
  const [openProfileFormModal, setOpenProfileFormModal] = useState(false);
  const user = useSelector((state: RootState) => state.user);
  const handleProfileUpdate = () => {
    setOpenProfileFormModal(true);
  };

  return (
    <div className="flex justify-center items-center w-full mt-20">
      <div className="py-8 bg-[#141517] rounded-[30px] border border-[#707070] w-[800px] h-fit">
        <div className="flex flex-col gap-4 items-center justify-center relative mr-28">
          <Image
            alt=""
            src="/icons/avatar.png"
            width={80}
            height={80}
            className="rounded-full bg-gray-50"
          />
          <span className="text-white font-bold text-2xl">{user.name}</span>
          <button
            className="absolute top-6 right-16"
            onClick={handleProfileUpdate}
          >
            <PencilSquareIcon
              aria-hidden="true"
              className="h-6 w-6 shrink-0 font-semibold text-white ml-3 cursor-pointer hover:text-red-500 transition-all"
            />
          </button>
        </div>
        <div className="w-[500px] mx-auto mt-8 space-y-2">
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">Email</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.email}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">Phone</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.phone}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">
                PAN Number
              </span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.pan_number}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">UPI ID</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.upi_id}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">Instagram</span>
            </div>
            <div>
              {user.instagram_link ? (
                <a 
                  href={user.instagram_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="font-semibold text-base text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {user.instagram_link}
                </a>
              ) : (
                <div className="flex items-center">
                  <span className="text-gray-400 text-sm italic">
                    Add your Instagram link in profile settings
                  </span>
                  <button 
                    onClick={handleProfileUpdate}
                    className="ml-2 text-blue-400 hover:text-blue-300 text-sm underline transition-colors"
                  >
                    Add now
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">YouTube</span>
            </div>
            <div>
              {user.youtube_link ? (
                <a 
                  href={user.youtube_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="font-semibold text-base text-red-400 hover:text-red-300 transition-colors"
                >
                  {user.youtube_link}
                </a>
              ) : (
                <div className="flex items-center">
                  <span className="text-gray-400 text-sm italic">
                    Add your YouTube link in profile settings
                  </span>
                  <button 
                    onClick={handleProfileUpdate}
                    className="ml-2 text-red-400 hover:text-red-300 text-sm underline transition-colors"
                  >
                    Add now
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-800 rounded-lg border border-gray-700">
            <p className="text-sm text-gray-300">
              📢Adding your YouTube and Instagram links will increase your visibility in the gaming community. 
              Your social media profiles will appear on the leaderboard, helping you connect with other gamers.
            </p>
          </div>
        </div>
      </div>
      <Modal
        modalOpen={openProfileFormModal}
        handleModalOpen={() => setOpenProfileFormModal(false)}
      >
        <UpdateProfileForm handleModal={setOpenProfileFormModal} user={user} />
      </Modal>
    </div>
  );
};

export default withAuth(ProfilePage);
