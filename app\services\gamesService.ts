import api from '../utils/axiosInstance';
import { API_ENDPOINTS } from '../constants/apiEndpoints';
import {
  Game,
  GameDetails,
  Lobby,
  GameBooking,
  GameResult,
  CreateGameBookingRequest,
  GamesResponse,
  GameBookingsResponse
} from '../types/CommonComponent.types';

export class GamesService {
  
  /**
   * Get list of all games
   */
  static async getAllGames(): Promise<GamesResponse> {
    const response = await api.get<GamesResponse | Game[]>(API_ENDPOINTS.GET_ALL_GAMES);
    
    // Handle different response formats
    if (Array.isArray(response.data)) {
      // Direct array response
      return {
        data: response.data,
        count: response.data.length
      };
    } else {
      // Structured response
      return response.data;
    }
  }

  /**
   * Get details of a specific game
   */
  static async getGameDetails(gameId: string): Promise<GameDetails> {
    const response = await api.get<GameDetails>(API_ENDPOINTS.GET_GAME_DETAILS(gameId));
    return response.data;
  }

  /**
   * Get all lobbies for a specific game
   */
  static async getGameLobbies(gameId: string): Promise<Lobby[]> {
    const response = await api.get<Lobby[]>(API_ENDPOINTS.GET_GAME_LOBBIES(gameId));
    return response.data;
  }

  /**
   * Get details of a specific lobby
   */
  static async getLobbyDetails(lobbyId: string): Promise<Lobby> {
    const response = await api.get<Lobby>(API_ENDPOINTS.GET_LOBBY_DETAILS(lobbyId));
    return response.data;
  }

  /**
   * Create a new game booking
   */
  static async createGameBooking(bookingData: CreateGameBookingRequest): Promise<GameBooking> {
    const response = await api.post<GameBooking>(
      API_ENDPOINTS.CREATE_GAME_BOOKING,
      bookingData
    );
    return response.data;
  }

  /**
   * Get game result details
   */
  static async getGameResult(resultId: string): Promise<GameResult> {
    const response = await api.get<GameResult>(API_ENDPOINTS.GET_GAME_RESULT(resultId));
    return response.data;
  }

  /**
   * Get user's game bookings
   */
  static async getGameBookings(page: number = 1, status?: string): Promise<GameBookingsResponse> {
    let endpoint = `${API_ENDPOINTS.GET_GAME_BOOKINGS}?page=${page}`;
    if (status) {
      endpoint += `&status=${status}`;
    }
    const response = await api.get<GameBookingsResponse>(endpoint);
    return response.data;
  }
}
