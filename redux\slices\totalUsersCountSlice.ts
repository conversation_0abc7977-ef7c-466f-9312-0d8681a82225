import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/app/utils/axiosInstance";

interface TotalUsersCountState {
  count: number;
}

const initialState: TotalUsersCountState = {
  count: 0,
};

// Async thunk to fetch total users count
export const fetchUsersCount = createAsyncThunk(
  "totalUsersCount/fetchUsersCount",
  async () => {
    const response = await api.get("/users-count");
    return response.data.count;
  }
);

const totalUsersCountSlice = createSlice({
  name: "totalUsersCount",
  initialState,
  reducers: {
    setTotalUsersCount: (state, action: PayloadAction<number>) => {
      state.count = action.payload;
    },
    clearTotalUsersCount: () => initialState,
  },
  extraReducers: (builder) => {
    builder.addCase(fetchUsersCount.fulfilled, (state, action) => {
      state.count = action.payload;
    });
  },
});

export const { setTotalUsersCount, clearTotalUsersCount } = totalUsersCountSlice.actions;
export default totalUsersCountSlice.reducer; 