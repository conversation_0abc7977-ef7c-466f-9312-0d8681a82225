"use client";
import { createContext, useContext, useState, ReactNode } from "react";

const SidebarContext = createContext<{
  isExpanded: boolean;
  toggleSidebar: (status: boolean) => void;
}>({
  isExpanded: true,
  toggleSidebar: () => {},
});

export const SidebarProvider = ({ children }: { children: ReactNode }) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);

  const toggleSidebar = (status: boolean) => {
    setIsExpanded(status);
  };

  return (
    <SidebarContext.Provider value={{ isExpanded, toggleSidebar }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebar = () => useContext(SidebarContext);
