import React from "react";
import Slider from "react-slick";
import { SliderBannerProps } from "@/app/types/CommonComponent.types";
import PromoBanner from "../promoBanner";
import { useSidebar } from "@/app/context/SidebarContext";

const SliderBanner: React.FC<SliderBannerProps> = ({ banners }) => {
  const { isExpanded } = useSidebar();
  const settings = {
    dots: true,
    infinite: true,
    speed: 200,
    slidesToShow: isExpanded ? 3 : 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  return (
    <div className="rounded-lg w-full">
      <Slider {...settings}>
        {banners.map((banner, index) => (
          <div className="px-2" key={index}>
            <PromoBanner {...banner} />
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default SliderBanner;
