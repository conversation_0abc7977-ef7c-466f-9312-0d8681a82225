export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return "NA";
  const [year, month, day] = dateString.split("-");
  const monthNames = new Map<number, string>([
    [1, "January"],
    [2, "February"],
    [3, "March"],
    [4, "April"],
    [5, "May"],
    [6, "June"],
    [7, "July"],
    [8, "August"],
    [9, "September"],
    [10, "October"],
    [11, "November"],
    [12, "December"],
  ]);

  return `${+day} ${monthNames.get(+month)} ${year}`;
};

export const handleNumericInput = (e: React.ChangeEvent<HTMLInputElement>) => {
  const input = e.target as HTMLInputElement;
  input.value = input.value.replace(/\D/g, "");
};

// app/services/userCountService.ts
import { AppDispatch } from "@/redux/store";
import {
  fetchUsersCount,
  setTotalUsersCount,
} from "@/redux/slices/totalUsersCountSlice";

export const fetchAndSetUsersCount = (dispatch: AppDispatch) => {
  return async () => {
    const count = await dispatch(fetchUsersCount()).unwrap();
    dispatch(setTotalUsersCount(count));
  };
};

export const capitalizeFirstLetter = (input: string): string => {
  if (input.length === 0) return input;
  return input.charAt(0).toUpperCase() + input.slice(1);
};

export const formatTime = (dateTimeString: string | null | undefined): string => {
  if (!dateTimeString) return "NA";
  
  // Parse the date string to create a Date object (in UTC)
  const date = new Date(dateTimeString);
  if (isNaN(date.getTime())) return "NA";
  
  // Convert to IST (UTC+5:30)
  const istDate = new Date(date.getTime() + (5 * 60 + 30) * 60 * 1000);
  
  // Extract hours, minutes and seconds
  const hours = istDate.getUTCHours();
  const minutes = istDate.getUTCMinutes().toString().padStart(2, '0');
  const seconds = istDate.getUTCSeconds().toString().padStart(2, '0');
  
  // Convert to 12-hour format
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const hour12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM
  
  return `${hour12}:${minutes}:${seconds} ${ampm} `;
};

export const formatDateToDDMMYYYY = (dateStr: string) => {
  const [year, month, day] = dateStr.split("-");
  return `${day}/${month}/${year}`;
};

/**
 * Generates a random PAN number with fixed 'ABCDE' prefix and random 4 digits + 1 letter
 * @returns A randomly generated PAN number in the format **********
 */
export const generateRandomPAN = (): string => {
  // Fixed prefix
  const prefix = "XXXXX";
  
  // Generate 4 random digits
  const randomDigits = Math.floor(1000 + Math.random() * 9000).toString();
  
  // Generate a random uppercase letter (A-Z)
  const randomLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
  
  // Combine to form the PAN number
  return `${prefix}${randomDigits}${randomLetter}`;
};
