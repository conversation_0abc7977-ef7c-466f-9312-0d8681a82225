import React, { useState, useEffect } from "react";
import {
  SelectScheduleCardProps,
  TimeSlot,
} from "@/app/types/CommonComponent.types";
import { ArrowUturnLeftIcon } from "@heroicons/react/24/outline";
import { formatDate } from "@/app/utils/helper";
import PlayerProgressBar from "../PlayerProgressBar";
import { ArrowRightIcon } from "@heroicons/react/16/solid";
import { Modal } from "../modal";
import StartGameModal from "../startGameModal";

const SelectScheduleCard: React.FC<SelectScheduleCardProps> = ({
  tournamentDetails,
  selectedTime,
  setSelectedTime,
  onNext,
  onBack,
  tournamentBookingsInfo,
  refreshBookingsData,
}) => {
  const handleTimeChange = (slot: TimeSlot) => {
    setSelectedTime(slot);
  };
  const [showGameDetailsModal, setShowGameDetailsModal] = useState(false);
  const [startGame, setStartGame] = useState<any>(null);

  const onStartGameClick = (slotId: number) => {
    const currentGame = tournamentBookingsInfo?.find(
      (booking: any) => booking.slot_id === slotId
    );
    setStartGame(currentGame);
    setShowGameDetailsModal(true);
  };

  // Watch for changes in tournamentBookingsInfo and update startGame if it's currently open
  useEffect(() => {
    if (startGame?.slot_id && tournamentBookingsInfo) {
      const updatedGame = tournamentBookingsInfo.find(
        (booking: any) => booking.slot_id === startGame.slot_id
      );
      if (updatedGame) {
        setStartGame(updatedGame);
      }
    }
  }, [tournamentBookingsInfo, startGame?.slot_id]);

  const handleRefreshGameDetails = async () => {
    // Call the refresh function to fetch fresh data from server
    if (refreshBookingsData) {
      try {
        const freshBookingsData = await refreshBookingsData();

        // Use the fresh data directly instead of waiting for state updates
        if (startGame?.slot_id && freshBookingsData) {
          const updatedGame = freshBookingsData.find(
            (booking: any) => booking.slot_id === startGame.slot_id
          );

          if (updatedGame) {
            setStartGame(updatedGame);
          }
        }
      } catch (error) {
        console.error('Failed to refresh game details:', error);
      }
    }
  };

  return (
    <>
      <div className="py-6 w-[800px] relative ">
        <button
          onClick={onBack}
          className="absolute -top-5 right-0  bg-red-500 text-[#070b28] rounded-full h-9 w-9 flex items-center justify-center font-semibold cursor-pointer"
        >
          <ArrowUturnLeftIcon className="h-5 w-5 font-semibold" />
        </button>
        <div className="text-center">
          <h2 className="text-white text-4xl font-semibold mb-3">
            {tournamentDetails?.name}
          </h2>
          <p className="text-white font-semibold text-xl mb-4">
            {tournamentDetails?.date}
          </p>
        </div>

        <div className="mt-4">
          <h4 className="w-full py-4 text-2xl bg-[#c9ff88] text-[#141517] text-center font-bold">
            Select Time Slots
          </h4>
        </div>

        <div className="mt-6">
          {tournamentDetails?.time_slots.map((slot, index) => (
            <div
              key={index}
              className="flex gap-12 mb-3 pb-3 border-b border-white items-center"
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  id={`slot-${index}`}
                  name="timeSlot"
                  value={slot.formatted_time}
                  checked={
                    selectedTime?.formatted_time === slot?.formatted_time
                  }
                  onChange={() => handleTimeChange(slot)}
                  className="mr-2 h-5 w-5 accent-[#F92C2C] appearance-none border-2 border-gray-400 bg-white checked:border-[#F92C2C] checked:bg-white relative before:content-[''] before:absolute before:top-1/2 before:left-1/2 before:transform before:-translate-x-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:bg-[#F92C2C] before:opacity-0 checked:before:opacity-100"
                  disabled={slot.is_expired || slot.is_cancelled}
                />
                <label
                  htmlFor={`slot-${index}`}
                  className={`text-white ${
                    slot.is_expired || slot.is_cancelled
                      ? "line-through opacity-50"
                      : ""
                  }`}
                >
                  <span>{slot?.formatted_time}, </span>
                  {formatDate(tournamentDetails?.date)}
                </label>
              </div>
              <div
                className={`flex-1 ${
                  slot.is_expired || slot.is_cancelled ? "opacity-50" : ""
                }`}
              >
                <PlayerProgressBar
                  bookingsCount={slot?.bookings_count ?? 0}
                  maxPlayers={slot?.max_players ?? 0}
                />
              </div>
              <div>
                <button
                  onClick={() => onStartGameClick(slot.id)}
                  className={`justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm min-w-[120px] whitespace-nowrap ${
                    slot.is_cancelled ||
                    !tournamentBookingsInfo?.find(
                      (booking: any) => booking.slot_id === slot.id
                    )?.booking_id
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-red-500"
                  }`}
                  disabled={
                    slot.is_cancelled ||
                    !tournamentBookingsInfo?.find(
                      (booking: any) => booking.slot_id === slot.id
                    )?.booking_id
                  }
                >
                  {slot.is_cancelled ? "Cancelled" : "Start Game"}
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 flex items-center justify-center ">
          <button
            onClick={onNext}
            disabled={!selectedTime}
            className={`bg-[#c9ff88] flex items-center justify-between text-[#131517] py-2 w-[80%] px-5 max-w-[455px] h-12 rounded-full hover:bg-[#a7f04e] transition-colors ${
              !selectedTime ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            <span className="text-base font-semibold">NEXT</span>
            <ArrowRightIcon
              aria-hidden="true"
              className="h-6 w-6 shrink-0 font-semibold"
            />
          </button>
        </div>
      </div>
      <Modal
        modalOpen={showGameDetailsModal}
        handleModalOpen={() => setShowGameDetailsModal(false)}
        showRefreshButton={true}
        onRefresh={handleRefreshGameDetails}
      >
        <StartGameModal
          game_link={(startGame?.game_link as string) || ""}
          room_id={(startGame?.room_id as string) || ""}
          room_password={(startGame?.room_password as string) || ""}
          bookingId={startGame?.booking_id}
          tournamentName={tournamentDetails?.name || ""}
          inGameName={startGame?.in_game_name || ""}
          showUpdateInGameNameForm={true}
          createdBy={tournamentDetails?.created_by}
        />
      </Modal>
    </>
  );
};

export default SelectScheduleCard;
