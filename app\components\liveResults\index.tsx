"use client";
import React, { useEffect, useState } from "react";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";

interface LiveResult {
  id: string;
  game_name: string;
  lobby_name: string;
  player_name: string;
  result: string;
  score?: number;
  rank: number;
  timestamp: string;
  game_image?: string;
}

const LiveResults: React.FC = () => {
  const [liveResults, setLiveResults] = useState<LiveResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchLiveResults = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.GET_LIVE_RESULTS);
      if (response.status === 200) {
        setLiveResults(response.data || []);
      }
    } catch (error) {
      console.error("Error fetching live results:", error);
      // Mock data for demonstration
      setLiveResults([
        {
          id: "1",
          game_name: "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>",
          lobby_name: "Quick Match",
          player_name: "Player123",
          result: "Win",
          score: 100,
          rank: 1,
          timestamp: new Date(Date.now() - 60000).toISOString(), // 1 min ago
          game_image: "/icons/tic4toe.png"
        },
        {
          id: "2",
          game_name: "Rock Paper Scissors",
          lobby_name: "Championship",
          player_name: "GameMaster",
          result: "Win",
          score: 150,
          rank: 1,
          timestamp: new Date(Date.now() - 180000).toISOString(), // 3 min ago
          game_image: "/icons/rockpaper.png"
        },
        {
          id: "3",
          game_name: "Dot & Box",
          lobby_name: "Pro League",
          player_name: "BoxKing",
          result: "Win",
          score: 200,
          rank: 1,
          timestamp: new Date(Date.now() - 300000).toISOString(), // 5 min ago
          game_image: "/icons/dot&box.png"
        },
        {
          id: "4",
          game_name: "Strategy Board",
          lobby_name: "Elite Arena",
          player_name: "Strategist",
          result: "Win",
          score: 300,
          rank: 1,
          timestamp: new Date(Date.now() - 480000).toISOString(), // 8 min ago
          game_image: "/icons/strategyboard.png"
        },
        {
          id: "5",
          game_name: "Tic-Tac-Toe",
          lobby_name: "Beginner Match",
          player_name: "NewPlayer",
          result: "Lose",
          score: 50,
          rank: 2,
          timestamp: new Date(Date.now() - 600000).toISOString(), // 10 min ago
          game_image: "/icons/tic4toe.png"
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveResults();
    
    // Poll for new results every 30 seconds
    const interval = setInterval(fetchLiveResults, 30000);
    return () => clearInterval(interval);
  }, []);

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const resultTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - resultTime.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    }
  };

  const getResultColor = (result: string) => {
    switch (result.toLowerCase()) {
      case 'win':
        return 'text-green-400';
      case 'lose':
        return 'text-red-400';
      case 'draw':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'text-yellow-400'; // Gold
      case 2:
        return 'text-gray-300'; // Silver
      case 3:
        return 'text-orange-400'; // Bronze
      default:
        return 'text-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-[#141517] rounded-[20px] border border-[#707070] p-6">
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
          <span className="w-3 h-3 bg-red-500 rounded-full mr-2 animate-pulse"></span>
          Live Results
        </h2>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-[#1a1a1c] rounded-lg p-3">
              <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#141517] rounded-[20px] border border-[#707070] p-6">
      <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
        <span className="w-3 h-3 bg-red-500 rounded-full mr-2 animate-pulse"></span>
        Live Results
        <span className="ml-2 text-sm font-normal text-gray-400">({liveResults.length} recent)</span>
      </h2>
      
      {liveResults.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎮</div>
          <p className="text-gray-400">No recent game results</p>
          <p className="text-sm text-gray-500 mt-2">Results will appear here as games complete</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {liveResults.map((result) => (
            <div key={result.id} className="bg-[#1a1a1c] rounded-lg p-3 hover:bg-[#2a2a2c] transition-all border border-[#2a2c2e]">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <img 
                    src={result.game_image || '/icons/twogames.png'} 
                    alt={result.game_name}
                    className="w-8 h-8 rounded object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/icons/twogames.png';
                    }}
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-white font-medium">{result.player_name}</span>
                      <span className={`font-bold ${getResultColor(result.result)}`}>
                        {result.result.toUpperCase()}
                      </span>
                      <span className={`text-sm ${getRankColor(result.rank)}`}>
                        #{result.rank}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      {result.game_name} • {result.lobby_name}
                      {result.score && <span> • {result.score} pts</span>}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {getTimeAgo(result.timestamp)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 pt-3 border-t border-[#2a2c2e]">
        <p className="text-xs text-gray-500 text-center">
          🔄 Updates every 30 seconds • Results from the last hour
        </p>
      </div>
    </div>
  );
};

export default LiveResults;
