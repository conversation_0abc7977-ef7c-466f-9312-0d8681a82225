"use client";
import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface GroupedGameCardProps {
  gameName: string;
  gameImage: string;
  type: string;
  tournamentCount: number;
}

const GroupedGameCard: React.FC<GroupedGameCardProps> = ({
  gameName,
  gameImage,
  type,
  tournamentCount,
}) => {
  const router = useRouter();

  const handleClick = () => {
    // Navigate to game detail page with game name and type as query parameters
    router.push(`/game-details?game=${encodeURIComponent(gameName)}&type=${encodeURIComponent(type)}`);
  };

  return (
    <div
      onClick={handleClick}
      className="bg-[#c9ff88] flex flex-col items-center rounded-[10px] pb-[4px] pl-2 pr-2 shadow-lg cursor-pointer mt-10"
    >
      <div className="-mt-10 relative h-[150px] w-[135px]">
        <Image
          src={gameImage || "/icons/gdutils.png"}
          alt={gameName}
          width={135}
          height={150}
          className="rounded-[10px] object-cover w-full h-full"
        />
      </div>
      <div className="flex items-center flex-col justify-center mt-1.5 gap-1">
        <span className="text-[#131517] text-sm font-semibold leading-none">
          {gameName}
        </span>
        <span className="mr-1 text-[#131517] text-sm font-semibold leading-none">
          {tournamentCount} {tournamentCount === 1 ? "Tournament" : "Tournaments"}
        </span>
      </div>
    </div>
  );
};

export default GroupedGameCard;