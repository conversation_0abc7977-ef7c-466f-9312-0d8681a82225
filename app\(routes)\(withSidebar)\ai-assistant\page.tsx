"use client";
import React, { useState, useRef, useEffect } from "react";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import { API_ENDPOINTS } from "../../../constants/apiEndpoints";

type Message = {
  role: "user" | "assistant";
  content: string;
};

type ErrorState = {
  isError: boolean;
  message: string;
  code?: number;
};

const AIAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ErrorState>({
    isError: false,
    message: "",
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: Message = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);
    setError({ isError: false, message: "" });

    try {
      
      const response = await axios.post(
        API_ENDPOINTS.AI_ASSISTANT,
        { message: input.trim() },
        { 
          headers: { "Content-Type": "application/json" },
          // Disable credentials to prevent sending cookies/auth headers that might trigger CORS preflight
          withCredentials: false
        }
      );

      if (response.data.success) {
        // Add assistant response to chat
        const assistantMessage: Message = {
          role: "assistant",
          content: response.data.message,
        };
        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        setError({
          isError: true,
          message: "Failed to get response from assistant",
        });
      }
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const statusCode = err.response?.status;
        let errorMessage = "An error occurred while communicating with the assistant";

        if (statusCode === 400) {
          errorMessage = "Invalid request. Please try again with a valid message.";
        } else if (statusCode === 429) {
          errorMessage = "You've reached the maximum number of requests. Please try again later.";
        } else if (statusCode === 500) {
          errorMessage = "Server error. Our team has been notified and is working on a fix.";
        }

        setError({
          isError: true,
          message: errorMessage,
          code: statusCode,
        });
      } else {
        setError({
          isError: true,
          message: "Failed to connect to the assistant service",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 flex flex-col h-[calc(100vh-70px)]">
      <h1 className="text-3xl font-bold text-white mb-6">AI Assistant</h1>
      
      <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-full mb-6 flex-1 flex flex-col overflow-hidden">
        <div className="flex p-6 flex-1 flex-col">
          {/* Chat messages container */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-3 flex-1 overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto pr-2 scrollbar-hide space-y-4 h-full max-h-[calc(100vh-250px)]">
              {messages.length === 0 ? (
                <div className="text-center text-gray-400 mt-10">
                  <p>Ask me anything about GamyDay!</p>
                </div>
              ) : (
                messages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} mb-3`}
                  >
                    <div
                      className={`max-w-[80%] rounded-md p-3 border ${message.role === "user" ? "bg-[#c9ff88] text-[#070b28] border-[#a7f04e]" : "bg-[#141517] text-white border-[#2a2c2e]"}`}
                    >
                      {message.role === "assistant" ? (
                        <div className="prose prose-invert max-w-none">
                          <ReactMarkdown
                            components={{
                              a: ({...props}) => (
                                <a 
                                  {...props} 
                                  className="text-[#c9ff88] underline hover:text-[#a7f04e]" 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                />
                              )
                            }}
                          >
                            {message.content}
                          </ReactMarkdown>
                        </div>
                      ) : (
                        <p className="font-semibold">{message.content}</p>
                      )}
                    </div>
                  </div>
                ))
              )}
              {isLoading && (
                <div className="flex justify-start mb-3">
                  <div className="max-w-[80%] rounded-md p-3 bg-[#141517] text-white border border-[#2a2c2e]">
                    <div className="flex space-x-2">
                      <div className="w-2 h-2 rounded-full bg-[#c9ff88] animate-bounce"></div>
                      <div className="w-2 h-2 rounded-full bg-[#c9ff88] animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                      <div className="w-2 h-2 rounded-full bg-[#c9ff88] animate-bounce" style={{ animationDelay: "0.4s" }}></div>
                    </div>
                  </div>
                </div>
              )}
              {error.isError && (
                <div className="flex justify-center mb-3">
                  <div className="max-w-[80%] rounded-md p-3 bg-red-500 text-white border border-red-600">
                    <p>{error.message}</p>
                    {error.code && <p className="text-sm mt-1">Error code: {error.code}</p>}
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input form */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
            <form onSubmit={handleSubmit}>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Type your message..."
                  disabled={isLoading}
                  className="flex-1 bg-[#141517] text-white rounded-md px-4 py-2 border border-[#2a2c2e] focus:outline-none focus:border-[#c9ff88]"
                />
                <button
                  type="submit"
                  disabled={isLoading || !input.trim()}
                  className={`bg-[#c9ff88] text-[#070b28] px-6 py-2 rounded-[10px] text-base font-semibold ${!input.trim() || isLoading ? "opacity-50 cursor-not-allowed" : "hover:bg-[#a7f04e] transition-colors"}`}
                >
                  Send
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      {/* Disclaimer note */}
      <div className="mt-4 text-center text-sm text-gray-400 italic">
        <p>
          Note: AI responses may not always be accurate. Please double-check important information or visit the official legal policies pages for precise information.
        </p>
      </div>
    </div>
  );
};

export default AIAssistant;