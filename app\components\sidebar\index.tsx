"use client";
import {
  CurrencyPoundIcon,
  GiftIcon,
  UserGroupIcon,
  GlobeAltIcon,
  PuzzlePieceIcon,
  Bars3Icon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { useSidebar } from "@/app/context/SidebarContext";

function classNames(...classes: any[]) {
  return classes.filter(Boolean).join(" ");
}

type NavigationItem = {
  name: string;
  href: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  iconImage?: string;
};

const Sidebar = () => {
  const pathname = usePathname();
  const { isExpanded, toggleSidebar } = useSidebar();
  // Group 1: Games and Features
  const gameFeatureNavigation: NavigationItem[] = [
    { name: "All Games", href: "/", icon: PuzzlePieceIcon },
    {
      name: "Gamix.io",
      href: "/gd-utiles",
      icon: UserGroupIcon,
      iconImage: "/icons/gdutils.png",
    },
    {
      name: "Leaderboard",
      href: "/leaderboard",
      icon: UserGroupIcon,
      iconImage: "/icons/leaderboard.png",
    },
    {
      name: "My Tournaments",
      href: "/my-tournaments",
      icon: GiftIcon,
      iconImage: "/icons/myTournament.png",
    },
    {
      name: "My Wallet",
      href: "/my-wallet",
      icon: CurrencyPoundIcon,
      iconImage: "/icons/wallet.png",
    },
    // {
    //   name: "AI Assistant",
    //   href: "/ai-assistant",
    //   icon: UserGroupIcon,
    //   iconImage: "/icons/ticketRaise.png",
    // },
    {
      name: "Raise Request",
      href: "/raise-request",
      icon: UserGroupIcon,
      iconImage: "/icons/ticketRaise.png",
    },
  ];

  // Group 2: Policies
  const policyNavigation: NavigationItem[] = [
    {
      name: "Terms & Conditions",
      href: "/terms-and-conditions",
      icon: GlobeAltIcon,
      iconImage: "/icons/terms.png",
    },
    {
      name: "Privacy Policy",
      href: "/privacy-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/privacy.png",
    },
    {
      name: "Refund Policy",
      href: "/refund-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/refund.png",
    },
    {
      name: "Joining Policy",
      href: "/joining-policy",
      icon: UserGroupIcon,
      iconImage: "/icons/gdutils.png",
    },
    {
      name: "Cancellation Policy",
      href: "/cancellation-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/cancellation.png",
    },
    {
      name: "Tax & Fee Policy",
      href: "/tax-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/tax.png",
    },
  ];

  // Group 3: About and Contact
  const aboutContactNavigation: NavigationItem[] = [
    {
      name: "Contact Us",
      href: "/contact",
      icon: GlobeAltIcon,
      iconImage: "/icons/contact.png",
    },
    {
      name: "About Us",
      href: "/about",
      icon: GlobeAltIcon,
      iconImage: "/icons/about.png",
    },
  ];

  return (
    <div
      className={`flex z-[999] bg-[#1a1c1e] border-r border-[#2a2c2e] ${
        isExpanded ? "min-w-[300px]" : "min-w-10"
      } fixed left-0`}
    >
      {/* Hamburger Button */}
      <button
        onClick={() => toggleSidebar(!isExpanded)}
        className="z-[999999] fixed top-24 left-8 bg-[#141517] rounded-lg p-2 border border-[#2a2c2e] hover:bg-[#1a1c1e] transition-colors"
      >
        <Bars3Icon className="w-6 h-6 text-[#c9ff88]" />
      </button>

      <aside className="h-[calc(100vh-135px)] flex grow flex-col gap-y-5 px-6 pb-4 mt-[50px] overflow-y-auto scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar-thin scrollbar-thumb-[#c9ff88] scrollbar-track-[#1a1c1e]">
        {/* Navigation Header */}
        {isExpanded && (
          <div className="bg-[#141517] rounded-lg p-4 border border-[#2a2c2e] mt-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-10 bg-[#c9ff88] rounded-sm"></div>
              <div>
                <p className="text-white text-xs uppercase tracking-wider opacity-70">Game</p>
                <p className="font-bold text-white text-lg">Navigation</p>
              </div>
            </div>
          </div>
        )}

        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" className="-mx-2 space-y-3">
                {/* Game Features Group */}
                {gameFeatureNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#141517] border-[#c9ff88] text-white"
                          : "bg-[#1a1c1e] border-[#2a2c2e] text-white hover:bg-[#141517] hover:border-[#c9ff88] group",
                        "group flex gap-x-3 rounded-lg py-3 px-4 text-base font-semibold leading-6 cursor-pointer transition-all duration-200 items-center border"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      <div className="flex items-center space-x-3 w-full">
                        {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                          <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        )}
                        <div className="flex items-center space-x-3">
                          {item.iconImage ? (
                            <Image
                              src={item.iconImage}
                              alt={item.name}
                              width={24}
                              height={24}
                              className="shrink-0"
                            />
                          ) : (
                            <item.icon
                              aria-hidden="true"
                              className="h-6 w-6 shrink-0 text-[#c9ff88]"
                            />
                          )}
                          {isExpanded && (
                            <div>
                              <p className="text-white text-sm font-semibold">{item.name}</p>
                              {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                                <p className="text-xs uppercase tracking-wider opacity-70 text-[#c9ff88]">Active</p>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}

                {/* Policies Section Header */}
                {isExpanded && (
                  <li className="px-2">
                    <div className="bg-[#141517] rounded-lg p-3 border border-[#2a2c2e] my-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        <p className="text-white text-xs uppercase tracking-wider opacity-70">Policies</p>
                      </div>
                    </div>
                  </li>
                )}

                {/* Policies Group */}
                {policyNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#141517] border-[#c9ff88] text-white"
                          : "bg-[#1a1c1e] border-[#2a2c2e] text-white hover:bg-[#141517] hover:border-[#c9ff88] group",
                        "group flex gap-x-3 rounded-lg py-3 px-4 text-base font-semibold leading-6 cursor-pointer transition-all duration-200 items-center border"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      <div className="flex items-center space-x-3 w-full">
                        {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                          <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        )}
                        <div className="flex items-center space-x-3">
                          {item.iconImage ? (
                            <Image
                              src={item.iconImage}
                              alt={item.name}
                              width={24}
                              height={24}
                              className="shrink-0"
                            />
                          ) : (
                            <item.icon
                              aria-hidden="true"
                              className="h-6 w-6 shrink-0 text-[#c9ff88]"
                            />
                          )}
                          {isExpanded && (
                            <div>
                              <p className="text-white text-sm font-semibold">{item.name}</p>
                              {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                                <p className="text-xs uppercase tracking-wider opacity-70 text-[#c9ff88]">Active</p>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}

                {/* Support Section Header */}
                {isExpanded && (
                  <li className="px-2">
                    <div className="bg-[#141517] rounded-lg p-3 border border-[#2a2c2e] my-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        <p className="text-white text-xs uppercase tracking-wider opacity-70">Support</p>
                      </div>
                    </div>
                  </li>
                )}

                {/* About and Contact Group */}
                {aboutContactNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#141517] border-[#c9ff88] text-white"
                          : "bg-[#1a1c1e] border-[#2a2c2e] text-white hover:bg-[#141517] hover:border-[#c9ff88] group",
                        "group flex gap-x-3 rounded-lg py-3 px-4 text-base font-semibold leading-6 cursor-pointer transition-all duration-200 items-center border"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      <div className="flex items-center space-x-3 w-full">
                        {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                          <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                        )}
                        <div className="flex items-center space-x-3">
                          {item.iconImage ? (
                            <Image
                              src={item.iconImage}
                              alt={item.name}
                              width={24}
                              height={24}
                              className="shrink-0"
                            />
                          ) : (
                            <item.icon
                              aria-hidden="true"
                              className="h-6 w-6 shrink-0 text-[#c9ff88]"
                            />
                          )}
                          {isExpanded && (
                            <div>
                              <p className="text-white text-sm font-semibold">{item.name}</p>
                              {(pathname === item.href || (pathname.startsWith(item.href) && item.name !== "All Games")) && (
                                <p className="text-xs uppercase tracking-wider opacity-70 text-[#c9ff88]">Active</p>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            </li>
          </ul>
        </nav>
      </aside>
    </div>
  );
};

export default Sidebar;
