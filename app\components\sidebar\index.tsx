"use client";
import {
  CurrencyPoundIcon,
  GiftIcon,
  UserGroupIcon,
  GlobeAltIcon,
  PuzzlePieceIcon,
  Bars3Icon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { useSidebar } from "@/app/context/SidebarContext";

function classNames(...classes: any[]) {
  return classes.filter(Boolean).join(" ");
}

type NavigationItem = {
  name: string;
  href: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  iconImage?: string;
};

const Sidebar = () => {
  const pathname = usePathname();
  const { isExpanded, toggleSidebar } = useSidebar();
  // Group 1: Games and Features
  const gameFeatureNavigation: NavigationItem[] = [
    { name: "All Games", href: "/", icon: PuzzlePieceIcon },
    {
      name: "Gamix.io",
      href: "/gd-utiles",
      icon: UserGroupIcon,
      iconImage: "/icons/gdutils.png",
    },
    {
      name: "Leaderboard",
      href: "/leaderboard",
      icon: UserGroupIcon,
      iconImage: "/icons/leaderboard.png",
    },
    {
      name: "My Tournaments",
      href: "/my-tournaments",
      icon: GiftIcon,
      iconImage: "/icons/myTournament.png",
    },
    {
      name: "My Wallet",
      href: "/my-wallet",
      icon: CurrencyPoundIcon,
      iconImage: "/icons/wallet.png",
    },
    // {
    //   name: "AI Assistant",
    //   href: "/ai-assistant",
    //   icon: UserGroupIcon,
    //   iconImage: "/icons/ticketRaise.png",
    // },
    {
      name: "Raise Request",
      href: "/raise-request",
      icon: UserGroupIcon,
      iconImage: "/icons/ticketRaise.png",
    },
  ];

  // Group 2: Policies
  const policyNavigation: NavigationItem[] = [
    {
      name: "Terms & Conditions",
      href: "/terms-and-conditions",
      icon: GlobeAltIcon,
      iconImage: "/icons/terms.png",
    },
    {
      name: "Privacy Policy",
      href: "/privacy-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/privacy.png",
    },
    {
      name: "Refund Policy",
      href: "/refund-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/refund.png",
    },
    {
      name: "Joining Policy",
      href: "/joining-policy",
      icon: UserGroupIcon,
      iconImage: "/icons/gdutils.png",
    },
    {
      name: "Cancellation Policy",
      href: "/cancellation-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/cancellation.png",
    },
    {
      name: "Tax & Fee Policy",
      href: "/tax-policy",
      icon: GlobeAltIcon,
      iconImage: "/icons/tax.png",
    },
  ];

  // Group 3: About and Contact
  const aboutContactNavigation: NavigationItem[] = [
    {
      name: "Contact Us",
      href: "/contact",
      icon: GlobeAltIcon,
      iconImage: "/icons/contact.png",
    },
    {
      name: "About Us",
      href: "/about",
      icon: GlobeAltIcon,
      iconImage: "/icons/about.png",
    },
  ];

  return (
    <div
      className={`flex z-[999] bg-[#141517] ${
        isExpanded ? "min-w-[300px]" : "min-w-10"
      } fixed left-0`}
    >
      {/* Hamburger Button */}
      <button
        onClick={() => toggleSidebar(!isExpanded)}
        className="z-[999999] fixed top-24 left-8 "
      >
        <Bars3Icon className="w-8 h-8 text-[#c9ff88]" />
      </button>
      <aside className=" h-[calc(100vh-135px)] flex grow flex-col gap-y-5  px-6 pb-4 mt-[50px] overflow-y-auto scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar-thin scrollbar-thumb-[#c9ff88] scrollbar-track-[#141517]">
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" className="-mx-2 space-y-2">
                {/* Game Features Group */}
                {gameFeatureNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#c9ff88] text-[#131517]"
                          : "text-[#c9ff88] hover:bg-[#c9ff88] hover:text-[#131517] group",
                        "group flex gap-x-3 rounded-full py-2.5 px-4 text-lg font-semibold leading-6 cursor-pointer transition-all duration-200 items-center"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      {item.iconImage ? (
                        <Image
                          src={item.iconImage}
                          alt={item.name}
                          width={30}
                          height={30}
                          className={`${
                            pathname.startsWith(item.href)
                              ? "grayscale brightness-0 bg-[#c9ff88]"
                              : ""
                          }group-hover:grayscale group-hover:brightness-0`}
                        />
                      ) : (
                        <item.icon
                          aria-hidden="true"
                          className="h-[30px] w-[30px] shrink-0"
                        />
                      )}
                      {isExpanded && <span>{item.name}</span>}
                    </Link>
                  </li>
                ))}
                
                {/* Divider after Raise Request */}
                <li className="px-2">
                  <div className="h-px bg-gray-700 my-2"></div>
                </li>
                
                {/* Policies Group */}
                {policyNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#c9ff88] text-[#131517]"
                          : "text-[#c9ff88] hover:bg-[#c9ff88] hover:text-[#131517] group",
                        "group flex gap-x-3 rounded-full py-2.5 px-4 text-lg font-semibold leading-6 cursor-pointer transition-all duration-200 items-center"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      {item.iconImage ? (
                        <Image
                          src={item.iconImage}
                          alt={item.name}
                          width={30}
                          height={30}
                          className={`${
                            pathname.startsWith(item.href)
                              ? "grayscale brightness-0 bg-[#c9ff88]"
                              : ""
                          }group-hover:grayscale group-hover:brightness-0`}
                        />
                      ) : (
                        <item.icon
                          aria-hidden="true"
                          className="h-[30px] w-[30px] shrink-0"
                        />
                      )}
                      {isExpanded && <span>{item.name}</span>}
                    </Link>
                  </li>
                ))}
                
                {/* Divider after Tax & Fee Policy */}
                <li className="px-2">
                  <div className="h-px bg-gray-700 my-2"></div>
                </li>
                
                {/* About and Contact Group */}
                {aboutContactNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      title={!isExpanded ? item.name : ""}
                      href={item.href}
                      className={classNames(
                        pathname === item.href ||
                          (pathname.startsWith(item.href) &&
                            item.name !== "All Games")
                          ? "bg-[#c9ff88] text-[#131517]"
                          : "text-[#c9ff88] hover:bg-[#c9ff88] hover:text-[#131517] group",
                        "group flex gap-x-3 rounded-full py-2.5 px-4 text-lg font-semibold leading-6 cursor-pointer transition-all duration-200 items-center"
                      )}
                      aria-current={pathname === item.href ? "page" : undefined}
                    >
                      {item.iconImage ? (
                        <Image
                          src={item.iconImage}
                          alt={item.name}
                          width={30}
                          height={30}
                          className={`${
                            pathname.startsWith(item.href)
                              ? "grayscale brightness-0 bg-[#c9ff88]"
                              : ""
                          }group-hover:grayscale group-hover:brightness-0`}
                        />
                      ) : (
                        <item.icon
                          aria-hidden="true"
                          className="h-[30px] w-[30px] shrink-0"
                        />
                      )}
                      {isExpanded && <span>{item.name}</span>}
                    </Link>
                  </li>
                ))}
              </ul>
            </li>
          </ul>
        </nav>
      </aside>
    </div>
  );
};

export default Sidebar;
