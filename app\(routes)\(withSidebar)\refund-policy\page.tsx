import React from "react";

const RefundPolicy = () => {
  return (
    <div className="p-10 space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Refund Policy</h1>

      <p className="text-lg font-medium text-white">
        At GamyDay, we strive to provide a fair and transparent refund process while maintaining the integrity of our platform. Please read the following terms carefully:
      </p>

      {/* <div className="border-b border-gray-600 my-6"></div> */}

      <div>
        <h2 className="text-2xl font-bold text-white mb-4">1. Eligibility for Refunds</h2>
        <ul className="list-disc pl-6 mb-4">
          <li className="text-lg font-medium text-white">
            Refunds are processed <em>only for tournaments officially canceled</em> by GamyDay.
          </li>
        </ul>
      </div>

    

      <div>
        <h2 className="text-2xl font-bold text-white mb-4">2. How to Request a Refund</h2>
        <ul className="list-disc pl-6 mb-4">
          <li className="text-lg font-medium text-white">
            Users must raise a refund request via the <em>&apos;Raise a Ticket&apos;</em> feature available on the platform.
          </li>
          <li className="text-lg font-medium text-white">
            The request must include:
            <ul className="list-disc pl-6 mt-2">
              <li className="text-lg font-medium text-white">Valid payment details</li>
              <li className="text-lg font-medium text-white">Relevant tournament information</li>
            </ul>
          </li>
        </ul>
      </div>

   

      <div>
        <h2 className="text-2xl font-bold text-white mb-4">3. Refund Method</h2>
        <ul className="list-disc pl-6 mb-4">
          <li className="text-lg font-medium text-white">
            Approved refunds will be issued to the <em>original payment method</em> used during the transaction.
          </li>
        </ul>
      </div>

     

      <div>
        <h2 className="text-2xl font-bold text-white mb-4">4. Non-Refundable Scenarios</h2>
        <ul className="list-disc pl-6 mb-4">
          <li className="text-lg font-medium text-white">
            No refunds will be issued in the following cases:
            <ul className="list-disc pl-6 mt-2">
              <li className="text-lg font-medium text-white">
                User fails to join due to <em>personal error, late entry, or technical issues not caused by GamyDay</em>
              </li>
              <li className="text-lg font-medium text-white">
                Missed participation due to:
                <ul className="list-disc pl-6 mt-2">
                  <li className="text-lg font-medium text-white">Internet failure</li>
                  <li className="text-lg font-medium text-white">Device compatibility issues</li>
                  <li className="text-lg font-medium text-white">User error</li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </div>

      

      <div>
        <h2 className="text-2xl font-bold text-white mb-4">5. Processing Time</h2>
        <ul className="list-disc pl-6 mb-4">
          <li className="text-lg font-medium text-white">
            Once approved, <em>refunds will be processed within 24 hours</em> of the request.
          </li>
        </ul>
      </div>
    </div>
  );
};

export default RefundPolicy;
