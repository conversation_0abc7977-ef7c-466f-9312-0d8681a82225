"use client";
import {
  EmailFormData,
  EmailFormSchema,
  PasswordFormData,
  PasswordFormSchema,
} from "@/app/schema/forgotPasswordSchema";
import api from "@/app/utils/axiosInstance";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import {
  EyeIcon,
  EyeSlashIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { handleNumericInput } from "@/app/utils/helper";
import { toast } from "react-toastify";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { withAuthRedirection } from "@/app/utils/withAuthRedirection";

const ForgotPassword: React.FC = () => {
  const [step, setStep] = useState(1);
  const [apiError, setApiError] = useState<string | null>(null);
  const router = useRouter();
  const [forgotPasswordData, setForgotPasswordData] = useState<any>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [countdown, setCountdown] = useState(30);
  const [isResendDisabled, setIsResendDisabled] = useState(true);

  useEffect(() => {
    if (step === 2) {
      if (countdown === 0) {
        setIsResendDisabled(false);
        return;
      }
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown, step]);

  const {
    register: registerEmail,
    handleSubmit: handleSubmitEmail,
    formState: { errors: emailErrors, isSubmitting: emailIsSubmitting },
  } = useForm<EmailFormData>({
    resolver: zodResolver(EmailFormSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    reset: resetPasswordForm,
    formState: { errors: passwordErrors, isSubmitting: passwordIsSubmitting },
  } = useForm<PasswordFormData>({
    resolver: zodResolver(PasswordFormSchema),
  });

  const onEmailSubmit: SubmitHandler<EmailFormData> = async (data) => {
    const reqData={...data,email:data.email.toLowerCase()}
    setApiError(null);
    try {
      const res = await api.post(API_ENDPOINTS.LOGIN_VERIFY, reqData);
      if (res.status === 200) {
        setForgotPasswordData({ ...reqData, otp_id: res?.data?.data?.otp_id });
        setStep(2);
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  const onPasswordSubmit: SubmitHandler<PasswordFormData> = async (data) => {
    try {
      const res = await api.post(API_ENDPOINTS.UPDATE_PASSWORD, {
        ...data,
        ...forgotPasswordData,
      });
      if (res.status === 200) {
        toast.success("Password updated successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        router.push("/login");
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  const onEmailChange = () => {
    setStep(1);
    resetPasswordForm();
    setCountdown(30);
  };

  const handleResendOtp = async () => {
    setCountdown(30);
    setIsResendDisabled(true);
    try {
      const res = await api.post(API_ENDPOINTS.GENERATE_OTP, {
        email: forgotPasswordData.email,
      });
      if (res.status === 200) {
        toast.success("OTP sent successfully on your email!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  return (
    <div className="min-h-[calc(100vh-84px)] flex items-center justify-center bg-dark-900 py-8">
      <div className="bg-dark-800 py-8 px-20 rounded-lg shadow-md w-[580px] bg-[#141517]">
        <h2 className="text-2xl font-semibold text-white mb-6 text-center">
          Rest your Password
        </h2>
        {apiError && (
          <div className="mb-4 p-2 bg-red-500 text-white rounded">
            {apiError}
          </div>
        )}
        {step === 1 ? (
          <form key="emailForm" onSubmit={handleSubmitEmail(onEmailSubmit)}>
            <div className="mb-4">
              <label className="block font-medium leading-6 text-white mb-2">
                Email
              </label>
              <input
                type="text"
                placeholder="Enter your email"
                className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                {...registerEmail("email")}
              />
              {emailErrors.email && (
                <p className="text-red-500 text-sm mt-1">
                  {emailErrors.email.message}
                </p>
              )}
            </div>
            <button
              type="submit"
              disabled={emailIsSubmitting}
              className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
            >
              {emailIsSubmitting ? "Proceeding..." : "Proceed"}
            </button>
          </form>
        ) : (
          <>
            <div className="mb-6">
              <p className="text-white text-center mb-4">
                We have sent a verification code on your email
              </p>
              <div className="flex justify-center">
                <span className="text-white font-bold text-xl">
                  {forgotPasswordData?.email}
                </span>
                <PencilSquareIcon
                  aria-hidden="true"
                  className="h-6 w-6 shrink-0 font-semibold text-white ml-3 cursor-pointer hover:text-red-500 transition-all"
                  onClick={onEmailChange}
                />
              </div>
            </div>
            <form
              key="passwordForm"
              onSubmit={handleSubmitPassword(onPasswordSubmit)}
            >
              <div className="mb-4">
                <label className="block font-medium leading-6 text-white mb-2">
                  OTP
                </label>
                <input
                  type="text"
                  maxLength={4}
                  placeholder="Enter OTP"
                  className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                  {...registerPassword("otp")}
                  onInput={handleNumericInput}
                />
                {passwordErrors.otp && (
                  <p className="text-red-500 text-sm mt-1">
                    {passwordErrors.otp.message}
                  </p>
                )}
              </div>
              <div className="mb-4">
                <label className="block font-medium leading-6 text-white mb-2">
                  New Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="New Password"
                    className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                    {...registerPassword("password")}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword((prev) => !prev)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                  >
                    {showPassword ? (
                      <EyeIcon
                        aria-hidden="true"
                        className="ml-2 h-5 w-5 text-black text-medium"
                      />
                    ) : (
                      <EyeSlashIcon
                        aria-hidden="true"
                        className="ml-2 h-5 w-5 text-black text-medium"
                      />
                    )}
                  </button>
                </div>
                {passwordErrors.password && (
                  <p className="text-red-500 text-sm mt-1">
                    {passwordErrors.password.message}
                  </p>
                )}
              </div>
              <div className="mb-4">
                <label className="block font-medium leading-6 text-white mb-2">
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm Password"
                    className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                    {...registerPassword("confirmPassword")}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                  >
                    {showConfirmPassword ? (
                      <EyeIcon
                        aria-hidden="true"
                        className="ml-2 h-5 w-5 text-black text-medium"
                      />
                    ) : (
                      <EyeSlashIcon
                        aria-hidden="true"
                        className="ml-2 h-5 w-5 text-black text-medium"
                      />
                    )}
                  </button>
                </div>
                {passwordErrors.confirmPassword && (
                  <p className="text-red-500 text-sm mt-1">
                    {passwordErrors.confirmPassword.message}
                  </p>
                )}
              </div>
              <button
                type="submit"
                disabled={passwordIsSubmitting}
                className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
              >
                {passwordIsSubmitting
                  ? "Reseting Password..."
                  : "Reset Password"}
              </button>
            </form>
            <div className="text-center mt-6">
              {isResendDisabled ? (
                <p className="text-white">Resend OTP in {countdown} seconds</p>
              ) : (
                <button
                  className="text-white underline"
                  onClick={handleResendOtp}
                  disabled={isResendDisabled}
                >
                  Resend OTP
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default withAuthRedirection(ForgotPassword);
