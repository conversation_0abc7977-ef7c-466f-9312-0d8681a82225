import { RedeemData } from "@/app/types/CommonComponent.types";

interface RedeemDetailsProps {
  redeemData: RedeemData;
}

const RedeemDetails = ({ redeemData }: RedeemDetailsProps) => {
  return (
    <>
      <div className="px-4 max-w-[500px]">
        <h3 className="text-3xl font-bold mb-4 text-white">Redeemed Details</h3>
        <div className="mb-5 space-y-1">
          <div className="grid grid-cols-2 gap-x-16">
            <div>
              <span className="font-semibold text-lg text-white">
                Redeemed Amount
              </span>
            </div>
            <div>
              <span className="font-medium text-lg text-white">
                ₹{Number(redeemData?.amount)?.toLocaleString("en-IN")}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-16">
            <div>
              <span className="font-semibold text-sm text-white">
                Tax Deducted (30%)
              </span>
            </div>
            <div>
              <span className="font-medium text-sm text-white">
                - ₹{Number(redeemData?.tax_amount)?.toLocaleString("en-IN")}
              </span>
            </div>
          </div>
          <hr className="border borer-white" />

          <div className="grid grid-cols-2 gap-x-16">
            <div>
              <span className="font-semibold text-lg text-white">
                Final Credited Amount
              </span>
            </div>
            <div>
              <span className=" text-lg font-semibold text-white">
                ₹{Number(redeemData?.credit_amount)?.toLocaleString("en-IN")}
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RedeemDetails;
