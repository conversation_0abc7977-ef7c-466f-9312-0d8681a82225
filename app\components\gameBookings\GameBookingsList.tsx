"use client";
import React, { useState, useEffect } from 'react';
import { GameBooking } from '@/app/types/CommonComponent.types';
import { GamesService } from '@/app/services/gamesService';
import Loader from '../common/Loader';
import { toast } from 'react-toastify';

interface GameBookingsListProps {
  limit?: number;
  showHeader?: boolean;
}

const GameBookingsList: React.FC<GameBookingsListProps> = ({ 
  limit,
  showHeader = true 
}) => {
  const [bookings, setBookings] = useState<GameBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    fetchBookings();
  }, [currentPage]);

  const fetchBookings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await GamesService.getGameBookings(currentPage);
      
      const newBookings = limit ? response.data.slice(0, limit) : response.data;
      
      if (currentPage === 1) {
        setBookings(newBookings);
      } else {
        setBookings(prev => [...prev, ...newBookings]);
      }
      
      setHasMore(!!response.next);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch bookings';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-400/20';
      case 'confirmed': return 'text-blue-400 bg-blue-400/20';
      case 'completed': return 'text-green-400 bg-green-400/20';
      case 'cancelled': return 'text-red-400 bg-red-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  if (isLoading && currentPage === 1) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        <p>Error: {error}</p>
        <button 
          onClick={fetchBookings}
          className="mt-2 px-4 py-2 bg-[#c9ff88] text-black rounded hover:bg-[#b8e877] transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {showHeader && (
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">Game Bookings</h2>
          {bookings.length > 0 && (
            <span className="text-gray-400 text-sm">{bookings.length} bookings</span>
          )}
        </div>
      )}

      {bookings.length === 0 && !isLoading ? (
        <div className="text-gray-400 text-center p-8">
          <p>No game bookings found.</p>
        </div>
      ) : (
        <div className="space-y-3">
          {bookings.map((booking) => (
            <div
              key={booking.booking_id}
              className="bg-gradient-to-r from-[#1a2c38] to-[#121f28] border border-[#2a4451] rounded-lg p-4 hover:border-[#c9ff88] transition-colors"
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-white font-bold text-lg">{booking.game_name}</h3>
                  <p className="text-gray-400 text-sm">Booking ID: {booking.booking_id}</p>
                </div>
                <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                </span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-400 block">Entry Fee</span>
                  <span className="text-[#c9ff88] font-semibold">₹{booking.entry_fee}</span>
                </div>
                <div>
                  <span className="text-gray-400 block">Booking Time</span>
                  <span className="text-white">{new Date(booking.booking_time).toLocaleDateString()}</span>
                </div>
                <div>
                  <span className="text-gray-400 block">Game Start</span>
                  <span className="text-white">{new Date(booking.game_start_time).toLocaleString()}</span>
                </div>
                {booking.result && (
                  <div>
                    <span className="text-gray-400 block">Prize</span>
                    <span className="text-[#c9ff88] font-semibold">₹{booking.result.prize_amount}</span>
                  </div>
                )}
              </div>

              {booking.result && (
                <div className="mt-3 pt-3 border-t border-[#2a4451]">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Position: {booking.result.position}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                      booking.result.status === 'win' ? 'bg-green-400/20 text-green-400' :
                      booking.result.status === 'lose' ? 'bg-red-400/20 text-red-400' :
                      'bg-gray-400/20 text-gray-400'
                    }`}>
                      {booking.result.status.toUpperCase()}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}

          {hasMore && !limit && (
            <div className="text-center">
              <button
                onClick={() => setCurrentPage(prev => prev + 1)}
                disabled={isLoading}
                className="px-6 py-2 bg-[#c9ff88] text-black rounded hover:bg-[#b8e877] transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GameBookingsList;
