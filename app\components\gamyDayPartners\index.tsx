import { useEffect, useState, useMemo } from "react";
import PartnerCard from "../partnerCard";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import {
  YoutubePartner,
  YoutubePartnerResponse,
} from "@/app/types/CommonComponent.types";
import { Modal } from "../modal";
import Pagination from "../pagination";
import Loader from "../common/Loader";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";

const GamyDayPartners = () => {
  const [youtubePartners, setYoutubePartners] =
    useState<YoutubePartnerResponse>({} as YoutubePartnerResponse);
  const [homePagePartners, setHomePagePartners] = useState<YoutubePartner[]>(
    []
  );
  const [showAllPartners, setShowAllPartners] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const itemsPerPage = 20;

  // Filter partners based on search query
  const filteredPartners = useMemo(() => {
    if (!searchQuery.trim()) {
      return youtubePartners?.results || [];
    }

    const query = searchQuery.toLowerCase().trim();
    return (youtubePartners?.results || []).filter((partner) =>
      partner.username.toLowerCase().includes(query)
    );
  }, [youtubePartners?.results, searchQuery]);

  const totalPages = Math.ceil(
    (searchQuery.trim() ? filteredPartners.length : youtubePartners?.count || 0) / itemsPerPage
  );

  // Get paginated filtered results
  const paginatedPartners = useMemo(() => {
    if (!searchQuery.trim()) {
      return youtubePartners?.results || [];
    }

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredPartners.slice(startIndex, endIndex);
  }, [filteredPartners, currentPage, itemsPerPage, searchQuery, youtubePartners?.results]);

  const fetchYoutubePartners = async () => {
    setLoading(true);
    try {
      const response = await api.get(
        API_ENDPOINTS.GET_YOUTUBE_PARTNERS(currentPage)
      );
      if (response.status === 200) {
        setYoutubePartners(response?.data);
        if (currentPage === 1) {
          setHomePagePartners(response?.data?.results);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchYoutubePartners();
  }, [currentPage]);

  // Reset to first page when search query changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  return (
    <>
      {homePagePartners?.length !== 0 ? (
        <section className="pt-6 px-4">
          <div className="flex justify-between items-center">
           
            <div
              className="text-base w-full text-right mb-4 font-semibold text-white underline cursor-pointer hover:text-[#c9ff88]"
              onClick={() => setShowAllPartners(true)}
            >
              Youtube Partners
            </div>
          </div>
          <div className="flex flex-wrap gap-10">
            {homePagePartners?.slice(0, 16)?.map((partner) => (
              <PartnerCard key={partner?.username} partner={partner} />
            ))}
          </div>
          <Modal
            modalOpen={showAllPartners}
            handleModalOpen={() => setShowAllPartners(false)}
          >
            <div className="w-[800px] h-[500px] flex flex-col">
              <div className="flex flex-col gap-4 mb-6">
                <h2 className="text-3xl font-bold text-white">
                  Youtube Partners
                </h2>

                {/* Search Bar */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    placeholder="Search YouTube partners..."
                    className="w-full pl-10 pr-10 py-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-[#c9ff88] text-black placeholder-gray-500"
                  />
                  {searchQuery && (
                    <button
                      onClick={clearSearch}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>

              {loading && <Loader />}

              <div className="flex-1">
                <div className="flex flex-wrap gap-10">
                  {!loading && (searchQuery.trim() ? paginatedPartners : youtubePartners?.results)?.map((partner) => (
                    <PartnerCard key={partner?.username} partner={partner} />
                  ))}
                </div>

                {/* Empty search state */}
                {!loading && searchQuery.trim() && filteredPartners.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-32 text-gray-400">
                    <MagnifyingGlassIcon className="h-12 w-12 mb-2" />
                    <p className="text-lg">No partners found</p>
                    <p className="text-sm">Try adjusting your search terms</p>
                  </div>
                )}
              </div>

              {(searchQuery.trim() ? filteredPartners.length : youtubePartners?.count) > 0 && totalPages > 1 && (
                <div className="border-t mt-4 pt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={(page) => setCurrentPage(page)}
                  />
                </div>
              )}
            </div>
          </Modal>
        </section>
      ) : (
        <></>
      )}
    </>
  );
};

export default GamyDayPartners;
