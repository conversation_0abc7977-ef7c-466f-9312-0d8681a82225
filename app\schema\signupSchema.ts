import { z } from "zod";

const phoneRegex = new RegExp("^[1-9][0-9]{9}$");
const PhoneSchema = z
  .string({ invalid_type_error: "Phone Number is required" })
  .min(1, { message: "Phone Number is required" })
  .max(10, { message: "Invalid Number" })
  .regex(phoneRegex, "Invalid Number");

export const SignupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z
    .string()
    .min(1, { message: "Email is required" })
    .email({ message: "Invalid email" }),
  phone: PhoneSchema,
  password: z.string().min(8, "Password must be at least 8 characters"),
  termsAndConditions: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions and privacy policy",
  }),
});

export type SignupFormData = z.infer<typeof SignupSchema>;
