import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "404",
  description: "Generated by Gamyday",
};

const NotFound = () => {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-84px)]">
      <div>
        <h2 className="text-center text-white font-semibold text-xl">
          Sorry, the page that you are looking for cannot be found. <br />
          Please check the URL or try navigating back to the homepage.
        </h2>
        <div className="flex justify-center my-10">
          <div className="flex gap-x-5">
            <Link href="/">
              <div className="flex justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 ">
                <span>Go back to the homepage</span>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
