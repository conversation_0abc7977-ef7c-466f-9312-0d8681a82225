import { z } from "zod";

export const RaiseRequestFormSchema = z
  .object({
    issue: z.object({
      id: z.number(),
      query: z.string().min(1, "Issue is required"),
    }),
    comments: z.string().min(1, "Please explain your issue or request in detail"),
    booking_id: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.issue.query === "Refunds & Cancellation") {
        return data.booking_id !== undefined && data.booking_id.trim() !== "";
      }
      return true;
    },
    {
      message: "Booking ID is required for Refunds & Cancellation",
      path: ["bookingId"],
    }
  );

export type RaiseRequestFormData = z.infer<typeof RaiseRequestFormSchema>;

