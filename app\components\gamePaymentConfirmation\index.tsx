"use client";
import React, { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface GamePaymentConfirmationProps {
  onPaymentComplete: () => void;
  onCancel: () => void;
}

const GamePaymentConfirmation: React.FC<GamePaymentConfirmationProps> = ({
  onPaymentComplete,
  onCancel,
}) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const user = useSelector((state: RootState) => state.user);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const bookingId = searchParams.get("booking_id");
  const lobbyId = searchParams.get("lobby_id");
  const amount = searchParams.get("amount");
  const gameId = searchParams.get("game_id");
  const username = searchParams.get("username");
  const type = searchParams.get("type");
  
  const [gameDetails, setGameDetails] = useState<any>(null);
  
  useEffect(() => {
    if (type === "game" && lobbyId) {
      fetchGameDetails();
    }
  }, [type, lobbyId]);
  
  const fetchGameDetails = async () => {
    try {
      // Fetch lobby details to show game info
      const response = await api.get(API_ENDPOINTS.GET_LOBBY_DETAILS(lobbyId || ""));
      if (response.status === 200) {
        setGameDetails(response.data);
      }
    } catch (error) {
      console.error("Error fetching game details:", error);
    }
  };
  
  const handleConfirmPayment = async () => {
    if (!bookingId || !amount) {
      setError("Missing payment details");
      return;
    }
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // Process the game payment
      const response = await api.post("/games/payments/confirm", {
        booking_id: bookingId,
        amount: parseFloat(amount),
      });
      
      if (response.status === 200) {
        // Payment successful, redirect to game play page with all necessary parameters
        const sessionId = response.data.session_id || bookingId; // Use session_id from response or fallback to booking_id
        router.push(`/game-play?sessionId=${sessionId}&gameId=${gameId}&username=${encodeURIComponent(username || user?.name || 'Player')}`);
        onPaymentComplete();
      }
    } catch (error: any) {
      console.error("Payment failed:", error);
      
      if (error.response?.status === 402) {
        setError("Insufficient balance. Please top up your wallet.");
      } else {
        setError("Payment failed. Please try again.");
      }
    } finally {
      setIsProcessing(false);
    }
  };
  
  if (type !== "game" || !bookingId || !amount) {
    return null;
  }
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#141517] border border-[#707070] rounded-lg p-6 max-w-md w-full mx-4">
        <h2 className="text-2xl font-bold text-white mb-4">Confirm Game Payment</h2>
        
        {gameDetails && (
          <div className="bg-[#1a1a1c] rounded-lg p-4 mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">{gameDetails.lobby_name}</h3>
            <p className="text-gray-400 text-sm mb-2">{gameDetails.lobby_desc}</p>
            <div className="text-sm text-gray-400">
              <div>Game: {gameDetails.game_name}</div>
              <div>Players: {gameDetails.current_players}/{gameDetails.max_players}</div>
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Playing as:</span>
            <span className="text-[#c9ff88] font-semibold">{username || user?.name || "Player"}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Entry Fee:</span>
            <span className="text-white font-semibold">₹{amount}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Current Balance:</span>
            <span className="text-[#c9ff88] font-semibold">₹{user?.wallet || 0}</span>
          </div>
          
          <div className="flex justify-between items-center border-t border-[#2a2c2e] pt-3">
            <span className="text-white font-semibold">After Payment:</span>
            <span className="text-white font-semibold">
              ₹{(user?.wallet || 0) - parseFloat(amount || "0")}
            </span>
          </div>
        </div>
        
        {error && (
          <div className="mt-4 text-red-500 text-sm bg-red-900 bg-opacity-20 p-3 rounded">
            {error}
          </div>
        )}
        
        <div className="flex space-x-3 mt-6">
          <button
            onClick={onCancel}
            className="flex-1 bg-gray-600 text-white py-2 rounded-md font-semibold hover:bg-gray-700 transition-all"
            disabled={isProcessing}
          >
            Cancel
          </button>
          <button
            onClick={handleConfirmPayment}
            className="flex-1 bg-[#c9ff88] text-[#070b28] py-2 rounded-md font-semibold hover:bg-opacity-90 transition-all disabled:opacity-50"
            disabled={isProcessing || (user?.wallet || 0) < parseFloat(amount || "0")}
          >
            {isProcessing ? "Processing..." : "Confirm Payment"}
          </button>
        </div>
        
        {(user?.wallet || 0) < parseFloat(amount || "0") && (
          <div className="mt-4 text-center">
            <button
              onClick={() => router.push("/my-wallet")}
              className="text-[#c9ff88] hover:underline text-sm"
            >
              Top up wallet first
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default GamePaymentConfirmation;
