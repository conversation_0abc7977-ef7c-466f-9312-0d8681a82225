import { YoutubePartner } from "@/app/types/CommonComponent.types";
import Link from "next/link";
interface PartnerCardProps {
  partner: YoutubePartner;
}

const PartnerCard: React.FC<PartnerCardProps> = ({ partner }) => {
  return (
    <Link
      href={partner?.youtube_link}
      target="_blank"
      className="flex flex-col items-center text-center hover:scale-105 transition-all duration-200 ease-in-out  h-fit"
    >
      <div className="relative w-12 h-12 rounded overflow-hidden border border-gray-600">
        <img
          src={partner?.image ?? ""}
          alt={partner?.username}
          className="object-cover absolute inset-0 w-full h-full"
        />
      </div>
      <h3 className="mt-2 font-semibold text-sm text-white">
        {partner?.username}
      </h3>
    </Link>
  );
};

export default PartnerCard;
