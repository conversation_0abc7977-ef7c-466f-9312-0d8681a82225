"use client";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { toast } from "react-toastify";
import {
  Option,
  RaiseRequestFormProps,
} from "@/app/types/CommonComponent.types";
import {
  RaiseRequestFormData,
  RaiseRequestFormSchema,
} from "@/app/schema/raiseRequestFormSchema";
import Select from "../formGroup/select";
import { useRouter } from "next/navigation";

const RaiseRequestForm: React.FC<RaiseRequestFormProps> = ({
  options,
  handleModal,
  prefillData,
  fetchData,
}) => {
  const [apiError, setApiError] = useState<string | null>(null);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<RaiseRequestFormData>({
    resolver: zodResolver(RaiseRequestFormSchema),
  });

  const selectedIssue = watch("issue");

  useEffect(() => {
    if (prefillData) {
      const selectedOption = options.find(
        (option) => option.query === prefillData.issue
      ) as Option;
      setValue("issue", selectedOption);
      setValue("booking_id", prefillData.booking_id);
      setSelectedOption(selectedOption || null);
    }
  }, [prefillData, options, setValue]);

  const handleSelectChange = (option: Option) => {
    setSelectedOption(option);
    setValue("issue", option);
  };

  const onSubmit = async (data: RaiseRequestFormData) => {
    const createRequest = {
      issue_id: data.issue.id,
      comments: data.comments,
      booking_id: data.booking_id ?? null,
    };
    setApiError(null);
    try {
      const res = await api.post(API_ENDPOINTS.RAISE_REQUEST, createRequest);
      if (res.status === 200) {
        toast.success("Request submitted successfully");
        router.push("/raise-request");
        fetchData?.();
        handleModal(false);
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  return (
    <div className="w-[450px]">
      <h2 className="text-2xl font-semibold text-white mb-6 text-center">
        Raise Request
      </h2>

      {apiError && (
        <div className="mb-4 p-2 bg-red-500 text-white rounded">{apiError}</div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Select Issue
          </label>
          <Select
            options={options}
            label="Select a Category"
            value={selectedOption}
            onChange={handleSelectChange}
          />
          {errors.issue && (
            <p className="text-red-500 text-sm mt-1">{errors.issue.message}</p>
          )}
        </div>

        {selectedIssue?.query === "Refunds & Cancellation" && (
          <div className="mb-4">
            <label className="block font-medium leading-6 text-white mb-2">
              Enter Booking ID*
            </label>
            <input
              type="text"
              className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
              placeholder="Booking ID"
              {...register("booking_id", {
                required: "Booking ID is required",
              })}
            />
            {errors.booking_id && (
              <p className="text-red-500 text-sm mt-1">
                {errors.booking_id.message}
              </p>
            )}
          </div>
        )}
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Explain Your Issue/Request*
          </label>
          <textarea
            className="w-full p-2 h-[260px] bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Please provide details about your issue or request"
            {...register("comments")}
          />
          {errors.comments && (
            <p className="text-red-500 text-sm mt-1">
              {errors.comments.message}
            </p>
          )}
        </div>
        <div className="flex gap-4 justify-end">
          <button
            onClick={() => handleModal(false)}
            className="px-4 py-2 bg-gray-600 text-white rounded-md w-full"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex justify-center w-full rounded-md bg-red-600 px-4 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {isSubmitting ? "Requesting..." : "Raise Request"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RaiseRequestForm;
