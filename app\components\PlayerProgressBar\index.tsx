interface PlayerProgressBarProps {
  bookingsCount: number;
  maxPlayers: number;
  small?: boolean;
}

const PlayerProgressBar: React.FC<PlayerProgressBarProps> = ({
  bookingsCount = 0,
  maxPlayers = 0,
  small = false,
}) => {
  const widthPercentage = `${Math.min(
    (bookingsCount / maxPlayers) * 100,
    100
  )}%`;
  const remainingPlayers = maxPlayers - bookingsCount;

  return (
    <div className={`${small ? 'pt-1' : 'pt-2'}`}>
      <div className={`${small ? 'h-1.5' : 'h-2'} bg-gray-700 rounded mt-1`}>
        <div
          style={{ width: widthPercentage }}
          className="h-full bg-red-600 rounded"
        ></div>
      </div>
      <div className="flex justify-between items-center mt-1">
        <p className={`${small ? 'text-xs' : 'text-base'} text-white`}>
          {remainingPlayers} {remainingPlayers > 1}{" "}
          remaining
        </p>
        <p className={`${small ? 'text-xs' : 'text-base'} text-white`}>
          {bookingsCount} {bookingsCount > 1} joined
        </p>
      </div>
    </div>
  );
};

export default PlayerProgressBar;
