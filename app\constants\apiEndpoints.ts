export const API_ENDPOINTS = {
  LOGIN: "/auth/login",
  LOGIN_VERIFY: "/auth/login-verify",
  SIGNUP: "/auth/signup",
  SIGNUP_VERIFY: "/auth/signup-verify",
  GENERATE_OTP: "/auth/generate-otp",
  UPDATE_PASSWORD: "/auth/password-update",
  GET_USER: "/user",
  GET_ALL_TOURNAMENTS: "/tournaments",
  GET_TOURNAMENT_DETAILS: (tournamentId: string) =>
    `/tournaments/${tournamentId}`,
  GET_TOURNAMENT_BOOKINGS_DETAILS: (tournamentId: string) =>
    `/tournaments/${tournamentId}/bookings`,
  CREATE_BOOKING_ORDER: "/payments/booking",
  GET_BOOKINGS: (page: number, status?: string) => `/user/bookings?page=${page}${status ? `&status=${status}` : ''}`,
  BOOKINGS: "/user/bookings",
  GET_BOOKING_DETAILS: (bookingId: string) => `/user/bookings/${bookingId}`,
  CREATE_IN_GAME_NAME: (bookingId: string) =>
    `/user/bookings/${bookingId}/in-game-name`,
  GET_BANNERS: "/banners",
  REDEEM: "/redeem",
  GET_ALL_REDEEM: (page: number) => `/redeem?page=${page}`,
  GET_RAISE_REQUEST: (page: number) => `/query?page=${page}`,
  RAISE_REQUEST: "/query",
  RAISE_REQUEST_OPTIONS: "/query-options",
  LEADERBOARD: (page: number) => `/leaderboard?page=${page}`,
  USER_LEADERBOARD_STATUS: "/user/leaderboard-status",
  REDEEM_DATA: "/redeem-data",
  REFRESH_TOKEN: "/auth/refresh",
  SOCIAL_ICONS: "/social-icons",
  CREATE_PAYMENT_ORDER: "/payments/order",
  PAYMENT_STATUS: (payment_id: string) => `/payments/order/${payment_id}`,
  GET_WALLET_TRANSACTIONS: (page: number) =>
    `/user/wallet/transactions?page=${page}`,
  GET_YOUTUBE_PARTNERS: (page: number) => `/youtube-partners?page=${page}`,
  REDEEM_WALLET: "/redeem-wallet",
  GET_ANNOUNCEMENTS: "/announcements",
  
  // Games API endpoints
  GET_ALL_GAMES: "/games",
  GET_GAME_DETAILS: (gameId: string) => `/games/${gameId}`,
  GET_GAME_LOBBIES: (gameId: string) => `/games/${gameId}/lobbies`,
  GET_LOBBY_DETAILS: (lobbyId: string) => `/lobbies/${lobbyId}`,
  CREATE_GAME_BOOKING: "/games/bookings",
  GET_GAME_RESULT: (resultId: string) => `/games/results/${resultId}`,

  GET_GAME_BOOKINGS: (page: number, status?: string) => `/games/bookings?page=${page}${status ? `&status=${status}` : ''}`,

  GET_GAME_SESSION: (sessionId: string) => `/games/sessions/${sessionId}`,
  GET_LIVE_RESULTS: "/games/live-results",
  
   // External API endpoints
   AI_ASSISTANT: "https://chatbot-gamyday.vercel.app/api/assistant",
};
