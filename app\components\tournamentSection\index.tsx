"use client";
import React, { useEffect, useMemo, useState } from "react";
import { TournamentSectionProps } from "@/app/types/CommonComponent.types";
import GroupedGameCard from "../groupedGameCard";
import { usePathname } from "next/navigation";
import HybridTournamentCard from "../hybridTournamentCard";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";

const TournamentSection: React.FC<TournamentSectionProps> = ({
  tournaments,
  sectionTitle,
}) => {
  const pathname = usePathname();
  const isGameDetailsPage = pathname === "/game-details";
  const [tournamentDetails, setTournamentDetails] = useState<any>(null);
  const [tournamentBookingsInfo, setTournamentBookingsInfo] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Group tournaments by game name if not on the game details page
  const groupedTournaments = useMemo(() => {
    if (isGameDetailsPage) return null;

    const groups = tournaments?.reduce((acc, tournament) => {
      const gameName = tournament.name;
      if (!acc[gameName]) {
        acc[gameName] = [];
      }
      acc[gameName].push(tournament);
      return acc;
    }, {} as Record<string, typeof tournaments>);

    return groups;
  }, [tournaments, isGameDetailsPage]);

  useEffect(() => {
    if (isGameDetailsPage && tournaments?.length > 0) {
      // Fetch details for all tournaments on the game details page
      fetchAllTournamentsDetails();
    }
  }, [isGameDetailsPage, tournaments]);

  const fetchAllTournamentsDetails = async () => {
    if (!tournaments || tournaments.length === 0) return;
    
    setIsLoading(true);
    try {
      const detailsPromises = tournaments.map(tournament => 
        api.get(API_ENDPOINTS.GET_TOURNAMENT_DETAILS(tournament.tournament_id))
      );
      
      const detailsResponses = await Promise.all(detailsPromises);
      const details = detailsResponses.map(res => res.data);
      setTournamentDetails(details);
      
      const bookingsPromises = tournaments.map(tournament => 
        api.get(API_ENDPOINTS.GET_TOURNAMENT_BOOKINGS_DETAILS(tournament.tournament_id))
          .then(res => res.data?.data || [])
          .catch(() => [])
      );
      
      const bookings = await Promise.all(bookingsPromises);
      setTournamentBookingsInfo(bookings);
    } catch (error) {
      console.error("Error fetching tournament details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshBookingsData = async (tournamentId: string) => {
    try {
      const res = await api.get(
        API_ENDPOINTS.GET_TOURNAMENT_BOOKINGS_DETAILS(tournamentId)
      );

      if (res.status === 200) {
        const freshData = res?.data?.data;
        // Update the bookings info for this specific tournament
        setTournamentBookingsInfo(prev => {
          const index = tournaments.findIndex(t => t.tournament_id === tournamentId);
          if (index === -1) return prev;
          
          const newBookings = [...prev];
          newBookings[index] = freshData;
          return newBookings;
        });
        return freshData;
      }
      return [];
    } catch {
      return [];
    }
  };

  const handleJoin = () => {
    // Refresh data after joining
    fetchAllTournamentsDetails();
  };

  return (
    <div className="">
      <div className="flex items-center gap-2 mb-5">
        <h1 className="text-2xl font-bold text-white">{sectionTitle}</h1>
      </div>

      {isGameDetailsPage ? (
        // Show hybrid tournament cards on game details page
        <div className="flex flex-col gap-6">
          {tournaments?.map((tournament, index) => (
            <HybridTournamentCard
              key={tournament?.tournament_id}
              tournamentDetails={tournamentDetails ? tournamentDetails[index] : null}
              tournamentBookingsInfo={tournamentBookingsInfo[index] || null}
              refreshBookingsData={() => refreshBookingsData(tournament.tournament_id)}
              onJoin={handleJoin}
              isLoading={isLoading || !tournamentDetails}
            />
          ))}
        </div>
      ) : (
        // Show grouped game cards on home page
        <div className="flex items-center gap-x-3 gap-y-6 justify-start flex-wrap">
          {groupedTournaments && Object.entries(groupedTournaments).map(([gameName, gameTournaments]) => (
            <GroupedGameCard
              key={`${sectionTitle}-${gameName}`}
              gameName={gameName}
              gameImage={gameTournaments[0]?.image || ""}
              type={sectionTitle}
              tournamentCount={gameTournaments.length}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TournamentSection;
